package ruleexecutor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"html"
	"os"
	"reflect"
	"slices"
	"strconv"
	"strings"
	"time"

	config "correspondence-composer/config"
	"correspondence-composer/gateways/policyapi"
	"correspondence-composer/mapper"
	helpers "correspondence-composer/mapper/helpers"
	"correspondence-composer/models"
	"correspondence-composer/utils"
	"correspondence-composer/utils/log"
)

var (
	ctx = context.Background()
)

const (
	CarrierData           = "Carrier"
	PolicyData            = "Policy"
	PolicyPreviousVersion = "PolicyPreviousVersion"
	PolicyMetric          = "PolicyMetric"
	Transaction           = "Transaction"
	PartyData             = "Party"
	ProductRates          = "ProductRates"
	XMLFileNameParams     = "XMLFileNameParams"
	PolicyFeature         = "PolicyFeature"
	EventData             = "EventData"
	Illustration          = "Illustration"
	ProductRate           = "ProductRate"
	RiderExerciseCharge   = "RiderExercise"
	AppImageData          = "AppImage"
	QualType              = "QualType"
	MCSData               = "MCS"
)

var (
	ErrNoResults           = errors.New("no results returned for rules")
	ErrBadPolicyDatapoint  = errors.New("this is not a policy data point")
	ErrInternalRuleFailure = errors.New("at least 1 internal rule failed, terminating process")
	ErrEmptyAPISlice       = errors.New("slice is empty")
)

var good bool

type RulesByLetterCfg map[string][]*models.RuleConfig

type RuleExecutor struct {
	Logger              log.Logger
	RulesEngine         rulesEngineGateway
	RulesByLetterConfig RulesByLetterCfg
	RulesConfig         []*models.RuleConfig
}

type rulesEngineGateway interface {
	ExecuteRules(rules []*models.Rule) (*models.RulesEngineResponse, error)
}

func (re *RuleExecutor) EvaluateRulesForEvent(ctx context.Context, data *models.AllPolicyData, event *models.EventEnvelope, eventTime string, config policyapi.Config) (*models.RulesResults, error) {
	defer utils.PanicHandler(ctx)

	var rules []*models.Rule
	var cycleDate string
	var rulesByEventConfig []*models.RuleConfig

	rulesByEventConfig = re.RulesByLetterConfig[event.EventType]
	if rulesByEventConfig == nil {
		rulesByEventConfig = re.RulesByLetterConfig["default"]
	}

	if event.EventType == "smoke-tests" {
		// handle smoke tests & check ALL rules
		rulesByEventConfig = re.RulesConfig
	}

	for _, identifier := range event.Identifiers {
		if identifier.IdentifierType == "originalEventTime" {
			if identifier.Value != "" {
				cycleDate = identifier.Value
			} else {
				re.Logger.Log(log.Info_Log, "OriginalEventTime from Event is nil:"+data.Policy.PolicyNumber, log.Fields{
					"CorrelationID":  event.CorrelationID,
					"eventType":      event.EventType,
					"policyNumber":   data.Policy.PolicyNumber,
					"MethodName":     "EvaluateRulesForEvent",
					"Sub-MethodName": "event.Identifiers Loop",
					"zlCaseID":       data.Policy.PolicyNumber, //need to find solution for the  events other than NB
				})
			}
		}
	}
	for _, ruleConfig := range rulesByEventConfig {
		rule, err := re.populateAllPolicyDataInputs(data, event.EventType, ruleConfig, eventTime, event, cycleDate, config)
		if err != nil {
			return nil, err
		}
		rules = append(rules, rule)
	}

	resp, err := re.RulesEngine.ExecuteRules(rules)
	if err != nil {
		return nil, err
	}

	if len(resp.Rules) < 1 {
		return nil, ErrNoResults
	}

	// handle internal rules if they failed / handle validations that terminate the process

	internalFailedValidations := re.internalRuleEvaluation(data.Policy)
	if len(internalFailedValidations) > 0 {
		re.Logger.Log(log.Err_Log, "EvaluateRulesForEvent Validation Failed", log.Fields{
			"ValidatePolicyData":        "at least 1 internal rule failed, terminating process...",
			"internalFailedValidations": internalFailedValidations,
			"MethodName":                "EvaluateRulesForEvent",
			"Sub-MethodName":            "internalRuleEvaluation",
			"policyNumber":              data.Policy.PolicyNumber,
		})
		return nil, ErrInternalRuleFailure
	}

	ruleResults := re.mapRuleResults(resp.Rules, data, event, rules)
	return ruleResults, nil

}

func (re *RuleExecutor) mapRuleResults(respRules []models.Rule, data *models.AllPolicyData, event *models.EventEnvelope, rules []*models.Rule) *models.RulesResults {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)
	mappedResults := &models.RulesResults{}
	var polRoleType string

	failedValidations := []*models.ValidationFailure{}
	xmlResults := []*models.XMLRuleResult{}
	partyRefs := []models.PartyRef{}
	riderExerciseCharge := []models.RiderExerciseChargeValues{}
	productRateXMLMappings := []models.ProductRateXMLMappings{}
	productRateSurrenderChargesXMLMappings := []models.ProductRateXMLSurrenderChrages{}
	appImageFilesXmlMapping := []*models.AppImageFile{}
	//policyValuesFromMetricsXMLMappings := []models.PolicyValuesMetricsXMLMappings{}
	//policyValuesFromMetricsXMLMappings := []models.XMLRuleResult{}

	for _, rule := range rules {
		re.Logger.Log(log.Info_Log, "Rule input for "+rule.RuleName+" for PolicyNumber:"+data.Policy.PolicyNumber, log.Fields{
			"ruleName":      rule.RuleName,
			"version":       rule.Version,
			"input":         rule.Input,
			"CorrelationID": event.CorrelationID,
			"eventType":     event.EventType,
			"policyNumber":  data.Policy.PolicyNumber,
		})
	}
	for _, resp := range respRules {
		re.Logger.Log(log.Info_Log, "Rule output for "+resp.RuleName+" for PolicyNumber:"+data.Policy.PolicyNumber, log.Fields{
			"ruleName":      resp.RuleName,
			"version":       resp.Version,
			"output":        resp.Output,
			"CorrelationID": event.CorrelationID,
			"eventType":     event.EventType,
			"policyNumber":  data.Policy.PolicyNumber,
		})
		if !re.isDataValid(resp.Output) {
			failedValidation := &models.ValidationFailure{
				RuleName: resp.RuleName,
				Response: resp.Input, // this part of the resp includes input and results
			}
			failedValidations = append(failedValidations, failedValidation)
		}

		newXMLValues, currentPolRoleType := re.getNewXMLValues(resp.RuleName, resp.Output)
		if currentPolRoleType != "" {
			polRoleType = currentPolRoleType
		}

		xmlResults = append(xmlResults, newXMLValues...)

		prodRatesMapping := re.getXMLProductRatesMappingParameter(resp.RuleName, resp.Output)
		productRateXMLMappings = append(productRateXMLMappings, prodRatesMapping...)

		prodRatesSurrenderChargeMapping := re.getXMLProductRateMappingsforSurrenderCharges(resp.RuleName, resp.Output)
		productRateSurrenderChargesXMLMappings = append(productRateSurrenderChargesXMLMappings, prodRatesSurrenderChargeMapping...)

		policyValueMapping := re.getXMLPolicyValuesFromMetrics(resp.RuleName, resp.Output)
		xmlResults = append(xmlResults, policyValueMapping...)

		partyRoles := re.GetPartyRoles(resp.Output, event)
		partyRefs = append(partyRefs, partyRoles...)

		riderExercise := re.getRiderExerciseChargeValues(resp.Output)
		riderExerciseCharge = append(riderExerciseCharge, riderExercise...)

		eventOutGoing := re.getEventOutGoing(resp.Output)
		if eventOutGoing != "" {
			mappedResults.EventOutGoing = eventOutGoing
		}

		transactionName := re.getTransactionName(resp.Output)
		if transactionName != "" {
			mappedResults.TransactionName = transactionName
		}
		fundId := re.setRequestedFundTransferAmount(resp.Output)
		if fundId != "" {
			mappedResults.FundId = fundId
		}
		lineOfBusiness := re.getProductLineOfBusiness(resp.Output)
		if lineOfBusiness != "" {
			mappedResults.LineOfBusiness = lineOfBusiness
		}
		xmlFileName := re.getXMLFileName(resp.Output)
		if xmlFileName != "" {
			mappedResults.XMLFileName = xmlFileName
		}

		deliveryInformation := re.getDeliveryInformation(resp.Output)
		if deliveryInformation.RelatedRoleID != "" {
			mappedResults.DeliveryRef = deliveryInformation
		}

		arrangementTypeToMap := re.getArrangementTypeToMap(resp.Output)
		if arrangementTypeToMap != "" {
			mappedResults.ArrangementTypeToMap = arrangementTypeToMap
		}
		InsuredData := re.getInsuredDataInfo(resp.Output)
		if InsuredData.Role != "" {
			mappedResults.InsuredRef = InsuredData
		}
		PolicyYearData := re.getIllustrationPolicyYearInfo(resp.Output)
		if PolicyYearData.IllustrationPolicyYear != 0 {
			mappedResults.PolicyYearDetails = PolicyYearData
		}
		IllOwnerData := re.getOwnerDataInfo(resp.Output)
		if IllOwnerData.IllustrationOwnerAge != 0 {
			mappedResults.ILLOwnerDetails = IllOwnerData
		}
		productCodeToCDSMap := re.getProductCodeToCDSMap(resp.Output)
		if productCodeToCDSMap != "" {
			mappedResults.ProductCodeToCDSMap = productCodeToCDSMap
		}

		agentMappingVariables := re.getAgentMappingVariables(resp.Output)
		if agentMappingVariables != nil {
			mappedResults.AgentMappingVariables = agentMappingVariables
		}

		carrierVariables := re.getCarrierNamePhone(resp.Output)
		if carrierVariables != nil {
			mappedResults.CarrierInfos = carrierVariables
		}

		appImageValues := re.getappImageValues(resp.Output)
		if appImageValues != nil {
			appImageFilesXmlMapping = append(appImageFilesXmlMapping, appImageValues...)
		}

		agentDeliveryInformation := re.getAgentDeliveryInformation(resp.Output)
		//if agentDeliveryInformation.RelatedRoleID != "" {
		if &agentDeliveryInformation != nil && agentDeliveryInformation.RelatedRoleID != "" {
			mappedResults.AgentDeliveryRef = agentDeliveryInformation
		}

		re.setCarrierBussinessSchdule(resp.Output, data)
		re.setPaymentStatus(resp.Output, data, event, resp.RuleName)
		re.setPremiumFrequency(resp.Output, data, event, resp.RuleName)
		re.setAppliedAmount(resp.Output, data, event, resp.RuleName)
		re.setAddressIndicator(resp.Output, data, event, resp.RuleName)
		re.setAddressStateIndicator(resp.Output, data, event, resp.RuleName)
		re.setDeliveryType(resp.Output, data, event, resp.RuleName)
		re.setDeliveryEmailNotification(resp.Output, data, event, resp.RuleName)
		re.setEmailAddressIndicator(resp.Output, data, event, resp.RuleName)
		//re.setMaxWithdrawalPercentage(resp.Output, data, event, resp.RuleName)
		re.calProductRateApiValues(resp.Output, data.ProductRates)
		re.setAPIEndpoints(resp.Output, data, event, resp.RuleName)
		re.setVendorInformation(resp.Output, data, event, resp.RuleName)
	}
	mappedResults.FailedValidations = failedValidations
	mappedResults.NewXMLValues = xmlResults
	mappedResults.PartyRefs = partyRefs
	mappedResults.ProductRateMapping = productRateXMLMappings
	mappedResults.RiderExerciseCharge = riderExerciseCharge
	mappedResults.ProductRateSurrenderChrages = productRateSurrenderChargesXMLMappings
	mappedResults.AppImageFiles = appImageFilesXmlMapping
	mappedResults.POLROLETYPE = polRoleType
	return mappedResults
}

func (re *RuleExecutor) internalRuleEvaluation(policy *models.Policy) []string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)
	var failedValidations []string
	if len(policy.ID) > 0 { //NB form update
		datesToValidate, err := mapper.GetAnnivDates(policy.PolicyDates.PreviousPolicyAnniversaryDate, policy.PolicyDates.NextAnniversaryDate, policy.PolicyDates.IssueDate)
		if err != nil || datesToValidate == nil {
			failedValidations = append(failedValidations, "VerifyAnniversaryDates")
			return failedValidations
		}

		hasNilDate := mapper.HasNilField(*datesToValidate)
		if hasNilDate {
			failedValidations = append(failedValidations, "VerifyAnniversaryDates")
		}
		return failedValidations
	}
	return nil

}

func (re *RuleExecutor) isDataValid(ruleOutput []models.RuleOutput) bool {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if !output.Valid && (output.Message != "") {
			return false
		}
	}

	return true
}

func (re *RuleExecutor) GetPartyRoles(ruleOutput []models.RuleOutput, event *models.EventEnvelope) []models.PartyRef {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	var PartyRefs models.PartyRef
	partyRoles := []models.PartyRef{}

	var roleCC = ""
	var intSlice []int
	for _, identifier := range event.Identifiers {
		if identifier != nil && identifier.IdentifierType == "ccRoleInfo" {
			if identifier.Value != "NA" {
				roleCC = identifier.Value
				// Split the string by commas
				strSlice := strings.Split(roleCC, ",")

				// Create a slice to store the integers
				intSlice = make([]int, len(strSlice))

				// Loop through the split strings and convert them to integers
				for i, s := range strSlice {
					num, err := strconv.Atoi(s)
					if err != nil {
						fmt.Println(err)
					}
					intSlice[i] = num
				}
			} else {
				intSlice = nil
			}
		}
	}

	for _, roleInformation := range ruleOutput {
		for _, rolesInfo := range roleInformation.RoleInformation {
			role := []byte(strings.ReplaceAll(rolesInfo.RoleInfo, "'", "\""))
			errRole := json.Unmarshal(role, &PartyRefs)

			if errRole != nil {
				fmt.Println(errRole)
			}
			PartyRefs.RoleCC = intSlice
			partyRoles = append(partyRoles, PartyRefs)
		}

	}
	return partyRoles
}

func (re *RuleExecutor) getappImageValues(ruleOutput []models.RuleOutput) []*models.AppImageFile {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	appImage := []*models.AppImageFile{}

	for _, output := range ruleOutput {

		if output.AppImageFiles != nil {
			for _, fld := range output.AppImageFiles {
				if fld.AppImageFileName != "N/A" {
					result := &models.AppImageFile{
						AppImageFilePath: fld.AppImageFilePath,
						AppImageFileName: fld.AppImageFileName,
						AppXMLTag:        fld.AppXMLTag,
					}
					appImage = append(appImage, result)
				}
			}

		}
	}
	return appImage
}

func (re *RuleExecutor) getNewXMLValues(ruleName string, ruleOutput []models.RuleOutput) ([]*models.XMLRuleResult, string) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)
	xmlRuleResults := []*models.XMLRuleResult{}
	polRoleType := ""
	flag := false

	for _, output := range ruleOutput {
		if output.NewXMLValue == "" || !strings.Contains(output.NewXMLValue, ":") {
			return nil, polRoleType
		}

		tagValuePairs := strings.Split(output.NewXMLValue, "^")
		for _, tagValuePair := range tagValuePairs {
			tagValues := strings.Split(tagValuePair, ":")

			result := &models.XMLRuleResult{
				RuleName: ruleName,
				XMLTag:   tagValues[0],
				XMLValue: tagValues[1],
			}
			if tagValues[0] == "POL_ROLE_TYPE" && !flag {
				polRoleType = tagValues[1]
				flag = true
			}
			xmlRuleResults = append(xmlRuleResults, result)
		}
	}
	return xmlRuleResults, polRoleType
}

func (re *RuleExecutor) getXMLPolicyValuesFromMetrics(ruleName string, ruleOutput []models.RuleOutput) []*models.XMLRuleResult {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	//Work to combine into 1 fuction with newXmlValues
	xmlRuleResults := []*models.XMLRuleResult{}
	for _, output := range ruleOutput {

		if len(output.NewPolicyMetricValues) == 0 {
			return nil
		}

		for _, mparm := range output.NewPolicyMetricValues {
			tagValues := strings.Split(mparm.ProductRatesMappingParamter, ":")
			if tagValues[0] != "" && tagValues[1] != "" {
				result := &models.XMLRuleResult{
					RuleName: ruleName,
					XMLTag:   tagValues[0],
					XMLValue: tagValues[1],
				}
				xmlRuleResults = append(xmlRuleResults, result)
			}
		}
	}

	return xmlRuleResults
}

func (re *RuleExecutor) getXMLProductRatesMappingParameter(ruleName string, ruleOutput []models.RuleOutput) []models.ProductRateXMLMappings {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	xmlRuleResults := []models.ProductRateXMLMappings{}

	for _, output := range ruleOutput {
		for _, mparm := range output.XMLProductRates {
			tagValues := strings.Split(mparm.ProductRatesMappingParamter, ":")
			result := models.ProductRateXMLMappings{
				XMLTag:    tagValues[0],
				Parameter: tagValues[1],
				BenID:     tagValues[2],
			}

			if len(tagValues) == 5 {
				result.NodeFieldName = tagValues[3]
				result.Value = tagValues[4]

			}

			xmlRuleResults = append(xmlRuleResults, result)
		}
	}

	return xmlRuleResults
}

func (re *RuleExecutor) getXMLProductRateMappingsforSurrenderCharges(ruleName string, ruleOutput []models.RuleOutput) []models.ProductRateXMLSurrenderChrages {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	xmlRuleResults := []models.ProductRateXMLSurrenderChrages{}

	for _, output := range ruleOutput {
		for _, mparm := range output.ProductRateSurrenderChrages {
			tagValues := strings.Split(mparm.ProductRateSurrenderChragesMapping, ":")
			result := models.ProductRateXMLSurrenderChrages{
				RateID:              tagValues[0],
				SurrenderChargeType: tagValues[1],
				APIEndpoint:         tagValues[2],
			}

			if len(tagValues) == 4 {
				result.Occurance = tagValues[3]

			}
			if len(tagValues) == 5 {
				result.FilterByYear, _ = strconv.ParseBool(tagValues[4])

			}

			xmlRuleResults = append(xmlRuleResults, result)
		}
	}

	return xmlRuleResults
}

func (re *RuleExecutor) getEventOutGoing(ruleOutput []models.RuleOutput) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var eventOutGoing string

	for _, output := range ruleOutput {
		if output.EventOutGoing == "" {
			return ""
		}
		eventOutGoing = output.EventOutGoing
	}
	return eventOutGoing
}

func (re *RuleExecutor) getTransactionName(ruleOutput []models.RuleOutput) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var transactionName string

	for _, output := range ruleOutput {
		if output.TransactionName == "" {
			return ""
		}
		transactionName = output.TransactionName
	}

	return transactionName
}
func (re *RuleExecutor) setRequestedFundTransferAmount(ruleOutput []models.RuleOutput) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var fundId string

	for _, output := range ruleOutput {
		if output.FundId != "" {
			return output.FundId
		}
		fundId = ""
	}

	return fundId
}

func (re *RuleExecutor) getArrangementTypeToMap(ruleOutput []models.RuleOutput) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var arrangementTypeToMap string

	for _, output := range ruleOutput {
		if output.ArrangementType == "" {
			return ""
		}
		arrangementTypeToMap = output.ArrangementType
	}

	return arrangementTypeToMap
}

func (re *RuleExecutor) getProductCodeToCDSMap(ruleOutput []models.RuleOutput) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var productCodeToCDS string

	for _, output := range ruleOutput {
		if output.ProductCodeToCDS == "" {
			return ""
		}
		productCodeToCDS = output.ProductCodeToCDS
	}

	return productCodeToCDS
}

func (re *RuleExecutor) getAgentMappingVariables(ruleOutput []models.RuleOutput) *models.AgentMappingVariable {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		for _, agentVariables := range output.AgentMappingVariables {
			if agentVariables.PlanCode != "" && agentVariables.PlanCode != "N/A" {
				return &agentVariables
			}
		}
	}

	return nil
}

func (re *RuleExecutor) getCarrierNamePhone(ruleOutput []models.RuleOutput) *models.CarrierInfo {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)
	var outputCarrier models.CarrierInfo
	for _, output := range ruleOutput {
		if output.CarrierName != "" && output.CarrierPhone != "" {
			outputCarrier.CarrierName = output.CarrierName
			outputCarrier.CarrierPhone = output.CarrierPhone
			return &outputCarrier
		}
	}

	return nil
}

func (re *RuleExecutor) setCarrierBussinessSchdule(ruleOutput []models.RuleOutput, data *models.AllPolicyData) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)
	for _, output := range ruleOutput {

		if output.BusinessDays != "" {
			data.Carrier.BusinessSchedule.BudgetCenter = output.BudgetCenter
			data.Carrier.BusinessSchedule.BusinessDays[0] = output.BusinessDays
			data.Carrier.BusinessSchedule.BusinessHourStart = output.BusinessHourStart
			data.Carrier.BusinessSchedule.BusinessHourEnd = output.BusinessHourEnd
		}

	}
}

func (re *RuleExecutor) setVendorInformation(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)
	for _, output := range ruleOutput {
		if len(output.MultipleVendorInformation) > 0 {
			for _, vinfo := range output.MultipleVendorInformation {
				vendorTagValuePairs := strings.Split(vinfo.AllVendorInformation, "^")
				// Create a new vendor object for each output
				var VendorData models.Vendor

				for _, tagValuePair := range vendorTagValuePairs {
					tagValues := strings.Split(tagValuePair, "|")
					if len(tagValues) != 2 {
						continue // Skip if the pair doesn't have exactly 2 parts
					}
					apiEndpoint := tagValues[0]
					xmlValue := tagValues[1]
					switch apiEndpoint {
					case "VendorBusinessName":
						VendorData.VendorBusinessName = xmlValue
					case "VendorAddrLine1":
						VendorData.VendorAddrLine1 = xmlValue
					case "VendorAddrLine2":
						VendorData.VendorAddrLine2 = xmlValue
					case "VendorCity":
						VendorData.VendorCity = xmlValue
					case "VendorState":
						VendorData.VendorState = xmlValue
					case "VendorZip":
						VendorData.VendorZip = xmlValue
					case "VendorPhnNum":
						VendorData.VendorPhnNum = xmlValue
					case "VendorEmailAddr":
						VendorData.VendorEmailAddr = xmlValue
					case "VendorWebLinkUrl":
						VendorData.VendorWebLinkUrl = xmlValue
					case "VendorReasonCode":
						VendorData.VendorReasonCode = xmlValue
					default:
						re.Logger.Log(log.Info_Log, "Unexpected vendor information", log.Fields{
							"CorrelationID": event.CorrelationID,
							"eventType":     event.EventType,
							"policyNumber":  data.Policy.PolicyNumber,
							"ruleName":      ruleName,
							"apiEndpoint":   apiEndpoint,
						})
					}
				}
				// Append the VendorData to the list
				data.NBEventData.Vendors = append(data.NBEventData.Vendors, &VendorData)
			}
		} else if output.VendorInformation != "" {
			vendorTagValuePairs := strings.Split(output.VendorInformation, "^")
			// Create a new vendor object for each output
			var VendorData models.Vendor

			for _, tagValuePair := range vendorTagValuePairs {
				tagValues := strings.Split(tagValuePair, "|")
				if len(tagValues) != 2 {
					continue // Skip if the pair doesn't have exactly 2 parts
				}
				apiEndpoint := tagValues[0]
				xmlValue := tagValues[1]
				switch apiEndpoint {
				case "VendorBusinessName":
					VendorData.VendorBusinessName = xmlValue
				case "VendorAddrLine1":
					VendorData.VendorAddrLine1 = xmlValue
				case "VendorAddrLine2":
					VendorData.VendorAddrLine2 = xmlValue
				case "VendorCity":
					VendorData.VendorCity = xmlValue
				case "VendorState":
					VendorData.VendorState = xmlValue
				case "VendorZip":
					VendorData.VendorZip = xmlValue
				case "VendorPhnNum":
					VendorData.VendorPhnNum = xmlValue
				case "VendorEmailAddr":
					VendorData.VendorEmailAddr = xmlValue
				case "VendorWebLinkUrl":
					VendorData.VendorWebLinkUrl = xmlValue
				case "VendorReasonCode":
					VendorData.VendorReasonCode = xmlValue
				default:
					re.Logger.Log(log.Info_Log, "Unexpected vendor information", log.Fields{
						"CorrelationID": event.CorrelationID,
						"eventType":     event.EventType,
						"policyNumber":  data.Policy.PolicyNumber,
						"ruleName":      ruleName,
						"apiEndpoint":   apiEndpoint,
					})
				}
			}
			// Append the VendorData to the list
			data.NBEventData.Vendors = append(data.NBEventData.Vendors, &VendorData)
		}
	}
}

func (re *RuleExecutor) setPaymentStatus(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if len(output.PaymentStatus) > 0 {

			for _, pstatus := range output.PaymentStatus {

				if !strings.Contains(pstatus.AllPaymentStatus, ":") {
					re.Logger.Log(log.Info_Log, "PaymentStatus value pairs are incorrect or missing :", log.Fields{
						"CorrelationID": event.CorrelationID,
						"eventType":     event.EventType,
						"policyNumber":  data.Policy.PolicyNumber,
						"ruleName":      ruleName,
						"paymentStatus": pstatus.AllPaymentStatus,
					})
					return
				}

				paymentStatusMapping := strings.Split(pstatus.AllPaymentStatus, ":")

				if paymentStatusMapping[0] != "" && paymentStatusMapping[1] != "" {
					for _, status := range data.Policy.SystematicPrograms {
						if status.Status == paymentStatusMapping[0] {
							status.Status = paymentStatusMapping[1]
						}
					}
				} else {
					re.Logger.Log(log.Info_Log, "PaymentStatus second value is missing", log.Fields{
						"CorrelationID": event.CorrelationID,
						"eventType":     event.EventType,
						"policyNumber":  data.Policy.PolicyNumber,
						"ruleName":      ruleName,
						"paymentStatus": pstatus.AllPaymentStatus,
					})
				}
			}
		}
	}
}
func (re *RuleExecutor) setAppliedAmount(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if len(output.AppliedAmount) > 0 {
			for _, trans := range data.Transactions {

				parsedFloat, err := strconv.ParseFloat(output.AppliedAmount, 64)
				if err != nil {
					re.Logger.Log(log.Info_Log, "Error parsing Applied Amount float:", log.Fields{
						"CorrelationID": event.CorrelationID,
						"eventType":     event.EventType,
						"policyNumber":  data.Policy.PolicyNumber,
						"ruleName":      ruleName,
						"paymentStatus": output.AppliedAmount,
					})
					return
				}

				// Assign the parsed float to the AppliedAmt field
				trans.TransactionAmounts.AppliedAmt = &parsedFloat
			}

		}
	}
}

func (re *RuleExecutor) calProductRateApiValues(ruleOutput []models.RuleOutput, data []*models.ProductRates) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {

		if len(output.ProductRateFormatted) > 0 {
			rateMapping := strings.Split(output.ProductRateFormatted, ":")

			if rateMapping != nil {
				for _, rates := range data {
					if rateMapping[0] == rates.RateId {
						rates.Rates.Ages.Age[0] = rateMapping[1]
					}
				}
			}
		}
	}
}

func (re *RuleExecutor) setMaxWithdrawalPercentage(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if len(output.ProductRateFormatted) > 0 {
			parsedFloat, err := strconv.ParseFloat(output.MaxWithdrawalPercentage, 64)
			if err != nil {
				re.Logger.Log(log.Info_Log, "Error parsing Max Withdrawal Percentage to float:", log.Fields{
					"CorrelationID": event.CorrelationID,
					"eventType":     event.EventType,
					"policyNumber":  data.Policy.PolicyNumber,
					"ruleName":      ruleName,
					"paymentStatus": output.MaxWithdrawalPercentage,
				})
				return
			}

			// Assign the parsed float to the AppliedAmt field
			data.Policy.WithDrawalValues.MaximumWithdrawalAmount = parsedFloat

		}
	}
}
func (re *RuleExecutor) setAPIEndpoints(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		apiPathValuePair := strings.Split(output.GeneralOutput, ":")
		if len(apiPathValuePair) > 1 {
			fieldPath := apiPathValuePair[0]
			value := apiPathValuePair[1]
			// Traverse through the struct fields
			fieldParts := strings.Split(fieldPath, ".")
			val := reflect.ValueOf(data).Elem()
			for _, part := range fieldParts {
				val = val.FieldByName(part)
				if !val.IsValid() {
					re.Logger.Log(log.Info_Log, "Field Not Found :", log.Fields{
						"CorrelationID": event.CorrelationID,
						"eventType":     event.EventType,
						"policyNumber":  data.Policy.PolicyNumber,
						"ruleName":      ruleName,
						"fields":        part,
					})
					//return
				}
				if val.Kind() == reflect.Ptr {
					if val.IsNil() {
						val.Set(reflect.New(val.Type().Elem()))
					}

					val = val.Elem()
				}
			}

			// Set the value dynamically
			if val.CanSet() {
				switch val.Kind() {
				case reflect.String:
					val.SetString(value)
				case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
					intValue, err := strconv.ParseInt(value, 10, 64)
					if err != nil {
						re.Logger.Log(log.Info_Log, "Error parsing int value :", log.Fields{
							"CorrelationID": event.CorrelationID,
							"eventType":     event.EventType,
							"policyNumber":  data.Policy.PolicyNumber,
							"ruleName":      ruleName,
							"field":         fieldPath,
							"value":         value,
							"error":         err.Error(),
						})
					}
					val.SetInt(intValue)
				case reflect.Bool:
					val.SetBool(value == "true")
				case reflect.Float32, reflect.Float64:
					parsedFloat, err := strconv.ParseFloat(value, 64)
					if err != nil {
						re.Logger.Log(log.Info_Log, "Error parsing float value:", log.Fields{
							"CorrelationID": event.CorrelationID,
							"eventType":     event.EventType,
							"policyNumber":  data.Policy.PolicyNumber,
							"ruleName":      ruleName,
							"field":         fieldPath,
							"value":         value,
							"error":         err,
						})
						return
					}
					val.SetFloat(parsedFloat)
				default:
					re.Logger.Log(log.Info_Log, "Unsupported field type :", log.Fields{
						"CorrelationID": event.CorrelationID,
						"eventType":     event.EventType,
						"policyNumber":  data.Policy.PolicyNumber,
						"ruleName":      ruleName,
						"field":         fieldPath,
						"value":         value,
					})
				}
			} else {
				re.Logger.Log(log.Info_Log, "Unsupported field type :", log.Fields{
					"CorrelationID": event.CorrelationID,
					"eventType":     event.EventType,
					"policyNumber":  data.Policy.PolicyNumber,
					"ruleName":      ruleName,
					"field":         fieldPath,
					"value":         value,
				})
			}

		}
	}
}

func (re *RuleExecutor) setAddressIndicator(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		for _, role := range data.Policy.PartyRoles {
			if role.PartyRole == output.AddressPartyId {
				for _, party := range data.Policy.Parties {
					if role.PartyID == party.PartyID {
						party.PreferredAddressIndicator = output.AddressId
					}
				}

			}
		}

		//multiple outputs

	}

}

func (re *RuleExecutor) setAddressStateIndicator(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if output.TargetRole != "" && (output.NewStateIndicator != "" || output.OldStateIndicator != "") {
			for _, role := range data.Policy.PartyRoles {
				if role.PartyRole == output.TargetRole {
					for _, party := range data.Policy.Parties {
						if role.PartyID == party.PartyID {
							if output.NewAddressId != "" || output.OldAddressId != "" { //to set new/old for Address
								for _, paddr := range party.Addresses {
									if paddr.RecordId == output.NewAddressId {
										paddr.ADDRCLASSIFICATION = output.NewStateIndicator
									}
									if paddr.RecordId == output.OldAddressId {
										paddr.ADDRCLASSIFICATION = output.OldStateIndicator
									}
								}
							}

							if output.NewEmailId != "" || output.OldEmailId != "" { //to set new/old for email
								for _, pemail := range party.Emails {
									if pemail.EmailID == output.NewEmailId {
										pemail.EMAILCLASSIFICATION = output.NewStateIndicator
									}
									if pemail.EmailID == output.OldEmailId {
										pemail.EMAILCLASSIFICATION = output.OldStateIndicator
									}
								}
							}

							if output.NewPhoneId != "" || output.OldPhoneId != "" { //to set new/old for Phone
								for _, pphone := range party.Phones {
									if pphone.PhoneId == output.NewPhoneId {
										pphone.PHNCLASSIFICATION = output.NewStateIndicator
									}
									if pphone.PhoneId == output.OldPhoneId {
										pphone.PHNCLASSIFICATION = output.OldStateIndicator
									}
								}
							}
						}
					}
				}
			}
		}
	}

}

func (re *RuleExecutor) setDeliveryType(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if output.TargetRole != "" && output.DeliveryType != "" {

			for _, role := range data.Policy.PartyRoles {
				if role.PartyRole == output.TargetRole { //|| (role.PartyRole == "PRIMARYSERVICINGAGENT")
					for _, party := range data.Policy.Parties {
						if role.PartyID == party.PartyID {
							party.DeliveryType = output.DeliveryType
						}
					}
				}
			}
			var caseStore *models.CaseStore
			for _, casestr := range event.CaseStore {
				if casestr.EntityType == "NB_SUITABILITY_DATA" {
					caseStore = casestr
				}
			}
			if caseStore != nil && len(caseStore.Entity.SuitabilityRequest.Parties) > 0 {
				for i := range caseStore.Entity.SuitabilityRequest.Parties {
					party := &caseStore.Entity.SuitabilityRequest.Parties[i]
					if party.RoleType == output.TargetRole {
						party.DeliveryType = output.DeliveryType
					}
				}
			}

			if event.DataValues.Payload != nil && event.DataValues.Payload.CoverageDetails != nil && len(event.DataValues.Payload.CoverageDetails.Owners) > 0 {
				event.DataValues.Payload.CoverageDetails.Owners[0].DeliveryType = output.DeliveryType
				event.DataValues.Payload.CoverageDetails.Agents[0].DeliveryType = output.DeliveryType
			} else {
				event.DataValues.DeliveryType = output.DeliveryType
			}
		}
	}

}

func (re *RuleExecutor) setDeliveryEmailNotification(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if output.TargetRole != "" && output.DeliveryEmailNotification != "" {
			for _, role := range data.Policy.PartyRoles {
				if role.PartyRole == output.TargetRole { //|| (role.PartyRole == "PRIMARYSERVICINGAGENT")
					for _, party := range data.Policy.Parties {
						if role.PartyID == party.PartyID {
							party.DeliveryEmailNotification = output.DeliveryEmailNotification
						}
					}
				}
			}

			if event.DataValues.Payload != nil && event.DataValues.Payload.CoverageDetails != nil && len(event.DataValues.Payload.CoverageDetails.Owners) > 0 {
				event.DataValues.Payload.CoverageDetails.Owners[0].EmailNotification = output.DeliveryEmailNotification
				event.DataValues.Payload.CoverageDetails.Agents[0].EmailNotification = output.DeliveryEmailNotification
			} else {
				event.DataValues.EmailNotification = output.DeliveryEmailNotification
			}

			var caseStore *models.CaseStore
			for _, casestr := range event.CaseStore {
				if casestr.EntityType == "NB_SUITABILITY_DATA" {
					caseStore = casestr
				}
			}

			if caseStore != nil && len(caseStore.Entity.SuitabilityRequest.Parties) > 0 {
				for i := range caseStore.Entity.SuitabilityRequest.Parties {
					party := &caseStore.Entity.SuitabilityRequest.Parties[i]
					if party.RoleType == output.TargetRole {
						party.EmailNotification = output.DeliveryEmailNotification
					}
				}
			}

		}

	}
}

func (re *RuleExecutor) setPremiumFrequency(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if len(output.PaymentFrequency) > 0 {

			for _, pstatus := range output.PaymentFrequency {

				if !strings.Contains(pstatus.AllPaymentFrequency, ":") {
					re.Logger.Log(log.Info_Log, "PremiumFrequency value pairs are incorrect or missing :", log.Fields{
						"CorrelationID":    event.CorrelationID,
						"eventType":        event.EventType,
						"policyNumber":     data.Policy.PolicyNumber,
						"ruleName":         ruleName,
						"premiumFrequency": pstatus.AllPaymentFrequency,
					})
					return
				}

				paymentFrequencyMapping := strings.Split(pstatus.AllPaymentFrequency, ":")

				if paymentFrequencyMapping[0] != "" && paymentFrequencyMapping[1] != "" {
					for _, frequency := range data.Policy.SystematicPrograms {
						if frequency.Frequency == paymentFrequencyMapping[0] {
							frequency.Frequency = paymentFrequencyMapping[1]
						}
					}
				} else {
					re.Logger.Log(log.Info_Log, "PremiumFrequency second value is missing", log.Fields{
						"CorrelationID":    event.CorrelationID,
						"eventType":        event.EventType,
						"policyNumber":     data.Policy.PolicyNumber,
						"ruleName":         ruleName,
						"premiumFrequency": pstatus.AllPaymentFrequency,
					})
				}
			}
		}
	}
}

func (re *RuleExecutor) getXMLFileName(ruleOutput []models.RuleOutput) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var xmlFileName string

	for _, output := range ruleOutput {
		if output.XMLFileName == "" {
			return ""
		}
		xmlFileName = output.XMLFileName
	}

	return xmlFileName
}

func (re *RuleExecutor) getProductLineOfBusiness(ruleOutput []models.RuleOutput) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var productLineOfBusiness string

	for _, output := range ruleOutput {
		if output.LineOfBusiness == "" {
			return ""
		}
		productLineOfBusiness = output.LineOfBusiness
	}

	return productLineOfBusiness
}

func (re *RuleExecutor) getDeliveryInformation(ruleOutput []models.RuleOutput) models.DeliveryRef {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)
	var deliveryRefs models.DeliveryRef

	for _, deliveryInformation := range ruleOutput {

		if deliveryInformation.DeliveryInfo != "" {
			fields := []byte(strings.ReplaceAll(deliveryInformation.DeliveryInfo, "'", "\""))
			errRole := json.Unmarshal(fields, &deliveryRefs)

			if errRole != nil {
				fmt.Println(errRole)
			}

		}
	}

	return deliveryRefs
}

func (re *RuleExecutor) getAgentDeliveryInformation(ruleOutput []models.RuleOutput) models.AgentDeliveryRef {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)
	var agnetdeliveryRefs models.AgentDeliveryRef

	for _, deliveryInformation := range ruleOutput {

		if deliveryInformation.AgentDeliveryInfo != "" {
			fields := []byte(strings.ReplaceAll(deliveryInformation.AgentDeliveryInfo, "'", "\""))
			errRole := json.Unmarshal(fields, &agnetdeliveryRefs)

			if errRole != nil {
				fmt.Println(errRole)
			}

		}
	}
	return agnetdeliveryRefs
}

func (re *RuleExecutor) getInsuredDataInfo(ruleOutput []models.RuleOutput) models.InsuredRef {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var insuredRefs models.InsuredRef

	for _, info := range ruleOutput {

		if info.IllustrationInsuredInfo != "" {
			fields := []byte(strings.ReplaceAll(info.IllustrationInsuredInfo, "'", "\""))
			errRole := json.Unmarshal(fields, &insuredRefs)

			if errRole != nil {
				fmt.Println(errRole)
			}

		}
	}
	return insuredRefs

}

func (re *RuleExecutor) getOwnerDataInfo(ruleOutput []models.RuleOutput) models.ILLOwnerDetails {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var ILLOwnerDetails models.ILLOwnerDetails

	for _, info := range ruleOutput {

		if info.IllustrationOwnerInfo != "" {
			fields := []byte(strings.ReplaceAll(info.IllustrationOwnerInfo, "'", "\""))
			errRole := json.Unmarshal(fields, &ILLOwnerDetails)

			if errRole != nil {
				fmt.Println(errRole)
			}

		}
	}
	return ILLOwnerDetails

}

func (re *RuleExecutor) getIllustrationPolicyYearInfo(ruleOutput []models.RuleOutput) models.PolicyYearDetails {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var policyyearinfo models.PolicyYearDetails

	for _, info := range ruleOutput {

		if info.IllustrationPolicyYearInfo != "" {
			fields := []byte(strings.ReplaceAll(info.IllustrationPolicyYearInfo, "'", "\""))
			errRole := json.Unmarshal(fields, &policyyearinfo)

			if errRole != nil {
				fmt.Println(errRole)
			}

		}
	}
	return policyyearinfo

}

func (re *RuleExecutor) getRiderExerciseChargeValues(ruleOutput []models.RuleOutput) []models.RiderExerciseChargeValues {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var chrgValue models.RiderExerciseChargeValues
	chrgValues := []models.RiderExerciseChargeValues{}

	for _, info := range ruleOutput {
		for _, riderChrgs := range info.RiderExerciseChrgs {
			rider := []byte(strings.ReplaceAll(riderChrgs.RiderExerciseValue, "'", "\""))
			errRider := json.Unmarshal(rider, &chrgValue)

			if errRider != nil {
				fmt.Println(errRider)
			}
			chrgValues = append(chrgValues, chrgValue)

		}
	}
	return chrgValues

}

func (re *RuleExecutor) getXMLFileNameParams(data *models.AllPolicyData, event string, eventtime string, inputKey string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	environment := config.GetEnvOrDefault("ENVIRONMENT", "development")
	envMap := map[string]interface{}{
		"local":       "DEV",
		"development": "DEV",
		"qa":          "QA",
		"production":  "PROD",
		"uat":         "UAT",
	}
	parsedTime, err := time.Parse(time.RFC3339, eventtime)
	month := parsedTime.Month()
	day := parsedTime.Day()
	year := parsedTime.Year() % 100
	// Format the extracted components to the desired layout "MMDDYY"
	formattedDate := fmt.Sprintf("%02d%02d%02d", month, day, year)
	carrier := data.Carrier
	timeStamp := time.Now().Format("150405")
	if err != nil {
		re.Logger.Log(log.Err_Log, err.Error(), log.Fields{
			"msg": "Date parsing error in getXMLFileNameParams",
		})
		return nil
	}
	XMLFileNameParams := map[string]interface{}{
		"environment":  envMap[environment],
		"cycleDate":    formattedDate,
		"policyNumber": data.Policy.PolicyNumber,
		"timestamp":    timeStamp,
		"clientCode":   carrier.CarrierDisplayName,
		"event":        event,
		"zlCaseID":     data.Policy.PolicyNumber,
	}
	result := XMLFileNameParams[inputKey]

	return result

}

func (re *RuleExecutor) populateAllPolicyDataInputs(data *models.AllPolicyData, incomingEvent string, ruleConfig *models.RuleConfig, eventTime string, event *models.EventEnvelope, cycleDate string, config policyapi.Config) (*models.Rule, error) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	inputMap := make(map[string]interface{})
	var returnError error
	for _, input := range ruleConfig.Inputs {
		inputKey := input["key"]
		if len(inputKey) < 1 {
			continue
		}

		inputValue := input["value"]
		delim := strings.Index(inputValue, ".")
		inputAPI := inputValue[0:delim]
		switch inputAPI {
		case CarrierData:
			inputMap[inputKey], returnError = re.getCarrierDatapoint(data.Carrier, inputKey)
		case PolicyData:
			inputMap[inputKey] = re.getPolicyDatapoint(data, incomingEvent, inputKey, eventTime, cycleDate)
		case PartyData:
			inputMap[inputKey] = re.getPartyDatapoint(data.Policy, inputKey, event)
		case PolicyPreviousVersion:
			if data.PolicyPreviousVersion != nil {
				inputMap[inputKey] = re.getPolicyPreviousVersionDatapoint(data.PolicyPreviousVersion, inputKey)
			}
		case PolicyMetric:
			if data.Metrics != nil {
				inputMap[inputKey] = re.getMetricDatapoint(data.Metrics, inputKey)
			}
		case ProductRate:
			if data.ProductRates != nil {
				inputMap[inputKey] = re.getProdutRatesDatapoint(data.ProductRates, inputKey)
			}
		case PolicyFeature:
			if data.Policy.PolicyFeatures != nil {
				inputMap[inputKey] = re.getPolicyFeaturesJsonDatapoint(data.Policy.PolicyFeatures, inputKey, event, data.Policy.PolicyNumber)
			} else {
				re.Logger.Log(log.Warning_Log, "PolicyFeatures are not available", log.Fields{
					"CorrelationID": event.CorrelationID,
					"eventType":     event.EventType,
					"policyNumber":  data.Policy.PolicyNumber,
				})
			}
		case Transaction:
			if data.Transactions != nil {
				inputMap[inputKey], returnError = re.getTransactionDatapoint(data.Transactions, inputKey)
			}
		case ProductRates:
			if data.ProductRates != nil {
				inputMap[inputKey] = re.getproductRatesDatapoint(data.ProductRates, inputKey)
			}
		case XMLFileNameParams:
			inputMap[inputKey] = re.getXMLFileNameParams(data, incomingEvent, eventTime, inputKey)
		case EventData:
			inputMap[inputKey] = re.getEventData(event, inputKey)
		case QualType:
			inputMap[inputKey] = re.getQualType(data, event, inputKey)

		case Illustration:
			inputMap[inputKey] = re.getIllusInsuredValidAgeCheck(data, inputKey)
		case RiderExerciseCharge:
			inputMap[inputKey] = re.getRiderExerciseCharge(data, inputKey, event)
		case AppImageData:
			inputMap[inputKey] = re.getAppImageDataPoint(inputKey, event, config)
		case MCSData:
			inputMap[inputKey], returnError = re.getMCSDatapoint(data.McsAgent, inputKey)
		default:
			err := ErrBadPolicyDatapoint
			re.Logger.Log(log.Err_Log, err.Error(), log.Fields{
				"CorrelationID":  event.CorrelationID,
				"eventType":      event.EventType,
				"policyNumber":   data.Policy.PolicyNumber,
				"ruleName":       ruleConfig.RuleName,
				"datapointKey":   inputAPI,
				"datapointValue": inputValue,
				"zlCaseID":       data.Policy.PolicyNumber, //need to find solution for the  events other than NB
			})
			return nil, err
		}

	}

	if returnError != nil {
		err := ErrEmptyAPISlice
		re.Logger.Log(log.Err_Log, err.Error(), log.Fields{
			"CorrelationID": event.CorrelationID,
			"eventType":     incomingEvent,
			"policyNumber":  data.Policy.PolicyNumber,
			"error":         returnError,
			"zlCaseID":      data.Policy.PolicyNumber, //need to find solution for the  events other than NB
		})
		return nil, err
	}

	rule := &models.Rule{
		RuleName: ruleConfig.RuleName,
		Version:  ruleConfig.Version,
		Input:    inputMap,
	}

	return rule, nil
}

func (re *RuleExecutor) getAppImageDataPoint(inputKey string, event *models.EventEnvelope, config policyapi.Config) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	appImageInputs := map[string]interface{}{
		"Event":            event.EventType,
		"CorrelationID":    event.CorrelationID,
		"Carrier":          event.Carrier,
		"AppImageFilePath": config.S3PolicyPageAppImagePath,
		"PolicyNumber":     event.DataValues.PolicyNumber,
		"PlanCode":         event.DataValues.PlanCode,
	}

	return appImageInputs

}

func (re *RuleExecutor) getPolicyPaymentDatapoint(data *models.AllPolicyData, policyPayments []*models.SystematicProgram, inputKey string, incomingEvent string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var policyPaymentInputs []map[string]interface{}

	for _, transdata := range data.Transactions {
		policyPaymentInputs := utils.GetArarngementID(policyPayments, transdata.ExternalTransID)
		if policyPaymentInputs == nil {
			return nil
		}
	}

	for _, policyPayment := range policyPayments {
		if (incomingEvent == config.Events.SystematicLoanRepaymentUpdate.Incoming) && policyPayment.Reason == "LOANREPAYMENT" {
			policyPaymentInput := map[string]interface{}{
				"arrangementID":    policyPayment.ArrangementID,
				"arrType":          policyPayment.ArrType,
				"status":           policyPayment.Status,
				"frequency":        policyPayment.Frequency,
				"paymentForm":      policyPayment.PaymentForm,
				"amount":           policyPayment.Amount,
				"requestedDate":    policyPayment.RequestedDate,
				"startDate":        policyPayment.StartDate,
				"endDate":          policyPayment.EndDate,
				"lastActivityDate": policyPayment.LastActivityDate,
				"nextActivityDate": policyPayment.NextActivityDate,
				"reason":           policyPayment.Reason,
			}
			policyPaymentInputs = append(policyPaymentInputs, policyPaymentInput)
		} else if incomingEvent == config.Events.AutoPaymentUpdate.Incoming && policyPayment.Reason == "PREMIUM" {
			policyPaymentInput := map[string]interface{}{
				"arrangementID":    policyPayment.ArrangementID,
				"arrType":          policyPayment.ArrType,
				"status":           policyPayment.Status,
				"frequency":        policyPayment.Frequency,
				"paymentForm":      policyPayment.PaymentForm,
				"amount":           policyPayment.Amount,
				"requestedDate":    policyPayment.RequestedDate,
				"startDate":        policyPayment.StartDate,
				"endDate":          policyPayment.EndDate,
				"lastActivityDate": policyPayment.LastActivityDate,
				"nextActivityDate": policyPayment.NextActivityDate,
				"reason":           policyPayment.Reason,
			}
			policyPaymentInputs = append(policyPaymentInputs, policyPaymentInput)
		}

		if policyPayment.SystematicProgramParty != nil {
			payorsInputs := map[string]interface{}{
				"partyRole":   policyPayment.SystematicProgramParty[0].PartyRole,
				"partyId":     policyPayment.SystematicProgramParty[0].PartyID,
				"partyBankId": policyPayment.SystematicProgramParty[0].FinancialInstitutionPartyID,
				"paymentFrom": policyPayment.SystematicProgramParty[0].PaymentForm,
				"percentage":  policyPayment.SystematicProgramParty[0].Percentage,
			}
			policyPaymentInputs = append(policyPaymentInputs, payorsInputs)
		} else {
			payorsInputs := map[string]interface{}{
				"partyRole":                   nil,
				"partyId":                     nil,
				"paymentFrom":                 nil,
				"financialInstitutionPartyID": nil,
				"percentage":                  nil,
			}
			policyPaymentInputs = append(policyPaymentInputs, payorsInputs)
		}
	}

	if inputKey == "paymentJson" {
		return policyPaymentInputs
	}

	result := policyPaymentInputs[0]["paymentJson"]

	return result
}

func (re *RuleExecutor) getCarrierDatapoint(carrier *models.Carrier, inputKey string) (interface{}, error) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	good, err := utils.CheckIsSliceEmpty(len(carrier.Addresses), "carrier.Addresses", "getCarrierDatapoint")
	if good == false {
		return nil, err
	}

	address := carrier.Addresses[0]

	addressStatus := "Active"
	if address.EndDate == "" {
		addressStatus = "Inactive"
	}

	good, err = utils.CheckIsSliceEmpty(len(carrier.Phones), "carrier.Phones", "getCarrierDatapoint")
	if good == false {
		return nil, err
	}
	//To remove carrierID in go code refactoring
	if !(carrier.CarrierID == "SBGC" || carrier.CarrierID == "SBL") {
		good, err = utils.CheckIsSliceEmpty(len(carrier.Emails), "carrier.Emails", "getCarrierDatapoint")
		if good == false {
			return nil, err
		}
	}

	emailAddress := ""
	if carrier.Emails != nil && len(carrier.Emails) > 0 && carrier.Emails[0].EmailAddress != "" {
		emailAddress = carrier.Emails[0].EmailAddress
	}

	carrieInputs := map[string]interface{}{
		"carrierID":           carrier.CarrierID,
		"orgCode":             carrier.CarrierOrgCode,
		"displayName":         carrier.CarrierDisplayName,
		"businessName":        carrier.CarrierBusinessName,
		"addressLine1":        address.AddrLine1,
		"addressCity":         address.City,
		"addressState":        address.State,
		"addressZipCode":      address.ZipCode,
		"addressZipExtension": address.ZipCodeExtension,
		"addressCountry":      address.AddrCountry,
		"addressStatus":       addressStatus,
		"businessPhone":       carrier.Phones[0].DialNumber,
		"emailAddress":        emailAddress, //email[0].EmailAddress,
		"website":             carrier.URL.Website,
		"areaCode":            carrier.Phones[0].AreaCode,
		"countryCode":         carrier.Phones[0].CountryCode,
		"depositoryAccName":   carrier.DepositoryAccount.NameOnAccount,
	}

	if carrier.BusinessSchedule != nil {
		carrieInputs["businessHourStart"] = carrier.BusinessSchedule.BusinessHourStart
		carrieInputs["businessHourEnd"] = carrier.BusinessSchedule.BusinessHourEnd

		good, err = utils.CheckIsSliceEmpty(len(carrier.BusinessSchedule.BusinessDays), "carrier.BusinessSchedule.BusinessDays", "getCarrierDatapoint")
		if good == false {
			return nil, err
		}

		carrieInputs["businessDays"] = carrier.BusinessSchedule.BusinessDays[0]
		carrieInputs["budgetCenter"] = carrier.BusinessSchedule.BudgetCenter
	}

	if inputKey == "carrierJson" {
		return carrieInputs, nil
	}

	result := carrieInputs[inputKey]

	return result, nil
}

func (re *RuleExecutor) getMCSDatapoint(mcs []*models.McsAgent, inputKey string) (interface{}, error) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	firstName := ""
	if mcs != nil && len(mcs) > 0 && len(mcs[0].Individuals) > 0 && mcs[0].Individuals != nil {
		firstName = mcs[0].Individuals[0].FirstName
	}
	mcsInputs := map[string]interface{}{
		"firstName": firstName,
	}

	if inputKey == "MCSJson" {
		return mcsInputs, nil
	}

	result := mcsInputs[inputKey]

	return result, nil
}

func (re *RuleExecutor) getProdutRatesDatapoint(prdRates []*models.ProductRates, inputKey string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	rateInputs := make(map[string]interface{})

	for _, rates := range prdRates {
		rateInputs[rates.RateId+"_Value"] = rates.Rates.Ages.Age[0]

	}

	return rateInputs

}

func (re *RuleExecutor) getMetricDatapoint(metrics []*models.PolicyMetric, inputKey string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var metricsNames []string
	metricsInputs := make(map[string]interface{})

	for _, metricsName := range metrics {
		metricsNames = append(metricsNames, metricsName.Metric)
	}

	metricNotReturned := checkIfMeticExists(metricsNames)

	for _, rmetricics := range metrics {

		metricsInputs[rmetricics.Metric] = rmetricics.Metric
		metricsInputs[rmetricics.Metric+"_MetricAvailable"] = "True"
		metricsInputs[rmetricics.Metric+"_Avg"] = rmetricics.Avg
		metricsInputs[rmetricics.Metric+"_Begin"] = utils.CheckForNilFloat(rmetricics.Begin)
		metricsInputs[rmetricics.Metric+"_Count"] = rmetricics.Count
		metricsInputs[rmetricics.Metric+"_End"] = utils.CheckForNilFloat(rmetricics.End)
		metricsInputs[rmetricics.Metric+"_Max"] = rmetricics.Max
		metricsInputs[rmetricics.Metric+"_Min"] = rmetricics.Min
		metricsInputs[rmetricics.Metric+"_Sum"] = rmetricics.Sum

	}

	for _, metricNameCheck := range metricNotReturned {
		metricsInputs[metricNameCheck] = metricNameCheck
		metricsInputs[metricNameCheck+"_MetricAvailable"] = "False"
		metricsInputs[metricNameCheck+"_Avg"] = nil
		metricsInputs[metricNameCheck+"_Begin"] = nil
		metricsInputs[metricNameCheck+"_Count"] = nil
		metricsInputs[metricNameCheck+"_End"] = nil
		metricsInputs[metricNameCheck+"_Max"] = nil
		metricsInputs[metricNameCheck+"_Min"] = nil
		metricsInputs[metricNameCheck+"_Sum"] = nil
	}

	return metricsInputs

}

func checkIfMeticExists(metricName []string) []string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var noMetricReturned []string

	metricNames := []string{"COSTOFINSURANCE",
		"INTERESTCREDITRATE",
		"LAPSEPROTECTIONPAYMENTAMOUNT",
		"EXPENSECHARGE",
		"UNITEXPENSECHARGE",
		"TOTALCOVERAGEAMOUNT",
		"CUMULATIVEGROSSDEATHBENEFITAMOUNT",
		"ACCOUNTSURRENDERVALUE",
		"ACCOUNTVALUE",
		"LOANREPAYMENTONETIMEAMOUNT",
		"SYSTEMATICLOANREPAYMENTAMOUNT",
		"INITIALPREMIUMAMOUNT",
		"SUBSEQUENTPREMIUMAMOUNT",
		"ONETIMEPREMIUMAMOUNT",
		"INTERESTCREDITAMOUNT",
		"NETDEATHBENEFIT",
	}

	for _, name := range metricNames {
		metricExists := slices.Contains(metricName, name)
		if metricExists == false {
			noMetricReturned = append(noMetricReturned, name)
		}
	}
	return noMetricReturned
}

func (re *RuleExecutor) getTransactionDatapoint(transactions []*models.Transaction, inputKey string) (interface{}, error) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	good, err := utils.CheckIsSliceEmpty(len(transactions), "[]*models.Transaction", "getTransactionDatapoint")
	if good == false {
		return nil, err
	}

	trans := transactions[0]

	transactionInputs := map[string]interface{}{
		"transactionId":         trans.TransactionID,
		"transactionType":       trans.TransactionType,
		"requestDate":           trans.RequestDate,
		"effectiveDate":         trans.EffectiveDate,
		"processDate":           trans.ProcessDate,
		"action":                trans.Action,
		"transactionReason":     trans.TransReason,
		"costOfInsurance":       helpers.GetChargeAmt("CostOfInsurance", "COSTOFINSURANCE", transactions),
		"totalExpenseCharge":    helpers.GetChargeAmt("ExpenseCharge", "EXPENSECHARGE", transactions),
		"reversalDate":          trans.ReversalDate,
		"originalTransactionID": trans.OriginalTransactionID,
	}

	if trans.PayeeOrBeneficiaries != nil && len(trans.PayeeOrBeneficiaries) > 0 {
		transactionInputs["partyRole"] = trans.PayeeOrBeneficiaries[0].PartyRole
		transactionInputs["partyId"] = trans.PayeeOrBeneficiaries[0].PartyID
		transactionInputs["paymentFrom"] = trans.PayeeOrBeneficiaries[0].PaymentForm
		transactionInputs["bankId"] = trans.PayeeOrBeneficiaries[0].FinancialInstitutionPartyID

	} else {
		transactionInputs["partyRole"] = nil
		transactionInputs["partyId"] = nil
		transactionInputs["paymentFrom"] = nil
		transactionInputs["bankId"] = nil

	}

	if trans.Payors != nil && len(trans.Payors) > 0 {
		transactionInputs["payorPartyRole"] = trans.Payors[0].PartyRole
		transactionInputs["payorPartyId"] = trans.Payors[0].PartyID
		transactionInputs["payorPaymentFrom"] = trans.Payors[0].PaymentForm
		transactionInputs["payorBankId"] = trans.Payors[0].FinancialInstitutionPartyID
	} else {
		transactionInputs["payorPartyRole"] = nil
		transactionInputs["payorPartyId"] = nil
		transactionInputs["payorPaymentFrom"] = nil
		transactionInputs["payorBankId"] = nil
	}

	if trans.TransactionAmounts != nil {
		//transactionInputs["paymentAmount"] = trans.TransactionAmounts.PaymentAmt
		transactionInputs["appliedAmount"] = utils.CheckForNilFloat(trans.TransactionAmounts.AppliedAmt)
		transactionInputs["paymentAmount"] = utils.CheckForNilFloat(trans.TransactionAmounts.PaymentAmt)
		transactionInputs["amountType"] = trans.TransactionAmounts.AmountType
	} else {
		//transactionInputs["paymentAmount"] = nil
		transactionInputs["appliedAmount"] = nil
		transactionInputs["paymentAmount"] = nil
		transactionInputs["amountType"] = nil

	}
	for _, f := range trans.FundActivities {
		transactionInputs[f.FundId+"_requestedFundTransferAmount"] = strconv.FormatFloat(f.AppliedAmount, 'f', 2, 64)
	}

	if inputKey == "transactionJson" {
		return transactionInputs, nil

	}
	result := transactionInputs

	return result, nil
}

func (re *RuleExecutor) getIllusInsuredValidAgeCheck(data *models.AllPolicyData, inputKey string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	illusData := data.Illustration
	pdata := data.Policy
	illusRoles := make(map[string]interface{})
	var numberOfRates int
	var rCGOI []interface{}

	for _, rate := range data.ProductRates {

		if rate.RateId == "rGCOI" {
			rCGOI = rate.Rates.Ages.Age
		}
		if rate.RateId == "pMaturityAge" {
			numberOfRates = int(rate.Rates.Ages.Age[0].(float64))
		}
	}

	//if illusData != nil && illusData.Results.Assumed != nil {
	if illusData != nil && rCGOI != nil {
		// Find the length of illusData.Results.Assumed.PolicyYear
		assumedLength := len(rCGOI)
		//fmt.Printf("Length of illusData.Results.Assumed.PolicyYear: %d\n", assumedLength)
		illusRoles["NoOfPolicyYears"] = assumedLength
		illusRoles["ISSUE_Age"] = pdata.Coverage.CoverageLayers[0].CoverageParticipants[0].IssueAge
		illusRoles["NumberOfRates"] = numberOfRates

		for _, pRoles := range pdata.PartyRoles {
			for _, party := range pdata.Parties {
				if party.PartyID == pRoles.PartyID {
					illusRoles[pRoles.PartyRole+"_Age"] = party.AttainedAge

				}
			}
		}
		if inputKey == "IllustrationJson" {
			return illusRoles
		}
	}

	return illusRoles
}

func (re *RuleExecutor) getRiderExerciseCharge(data *models.AllPolicyData, inputKey string, event *models.EventEnvelope) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	exerciseCharges := make(map[string]interface{})

	if data.Illustration != nil {
		if data.Illustration.Results != nil {
			if data.Illustration.Results.Assumed != nil {
				if len(data.Illustration.Results.Assumed.AnnualRiderCharges) > 0 {
					exerciseCharges["childRiderPayment"] = data.Illustration.Results.Assumed.AnnualRiderCharges[0]
				} else {
					re.Logger.Log(log.Info_Log, "data.Illustration.Results.Assumed.AnnualRiderCharges is nil:"+data.Policy.PolicyNumber, log.Fields{
						"CorrelationID":  event.CorrelationID,
						"policyNumber":   data.Policy.PolicyNumber,
						"MethodName":     "RuleExecutor",
						"Sub-MethodName": "getRiderExerciseCharge",
					})
				}
			} else {
				re.Logger.Log(log.Info_Log, "data.Illustration.Results.Assumed is nil:"+data.Policy.PolicyNumber, log.Fields{
					"CorrelationID":  event.CorrelationID,
					"policyNumber":   data.Policy.PolicyNumber,
					"MethodName":     "RuleExecutor",
					"Sub-MethodName": "getRiderExerciseCharge",
				})
			}
		} else {
			re.Logger.Log(log.Info_Log, "data.Illustration.Results is nil:"+data.Policy.PolicyNumber, log.Fields{
				"CorrelationID":  event.CorrelationID,
				"policyNumber":   data.Policy.PolicyNumber,
				"MethodName":     "RuleExecutor",
				"Sub-MethodName": "getRiderExerciseCharge",
			})
		}
	} else {
		re.Logger.Log(log.Info_Log, "data.Illustration is nil:"+data.Policy.PolicyNumber, log.Fields{
			"CorrelationID":  event.CorrelationID,
			"policyNumber":   data.Policy.PolicyNumber,
			"MethodName":     "RuleExecutor",
			"Sub-MethodName": "getRiderExerciseCharge",
		})
	}

	if inputKey == "riderExerciseJson" {
		return exerciseCharges
	}

	return exerciseCharges
}

// //rule input -- insured age+ no of policy year *1  = ageli9mt
// }

func (re *RuleExecutor) getAllPartyDatapoint(policy *models.Policy, inputKey string, event *models.EventEnvelope, preferredPhysicalAddressIndicator string, preferredEmailAddressIndicator string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	partyRoles := policy.PartyRoles
	parties := policy.Parties
	rolesNames := make(map[string]interface{})
	partyPolicyChangeReferenceId := event.DataValues.PartyPolicyChangeReferenceId
	partyPolicyNewReferenceId := event.DataValues.PartyPolicyNewReferenceId
	var targetRole string

	for _, identifier := range event.Identifiers {
		if identifier != nil && identifier.IdentifierType == "targetRole" {
			targetRole = identifier.Value
			//return targetRole
		}
	}

	/*ROLE:
	  PREFERREDADDLDING*/
	rolesNames["eventType"] = event.EventType
	rolesNames["partyPolicyChangeReferenceId"] = partyPolicyChangeReferenceId
	rolesNames["partyPolicyNewReferenceId"] = partyPolicyNewReferenceId
	rolesNames["targetRole"] = targetRole
	rolesNames["preferredPhysicalAddressIndicator"] = preferredPhysicalAddressIndicator
	rolesNames["preferredEmailAddressIndicator"] = preferredEmailAddressIndicator
	for _, roleName := range partyRoles {
		for _, party := range parties {
			if roleName.PartyRole == targetRole { //|| (roleName.PartyRole == "PRIMARYSERVICINGAGENT")
				rolesNames["partyRole"] = roleName.PartyRole
				if roleName.PartyID == party.PartyID {
					rolesNames["preferredAddressIndicator"] = party.PreferredAddressIndicator
					rolesNames["preferredCommunicationType"] = party.PrefCommunicationType
					rolesNames["preferredPhysicalAddressIndicator"] = preferredPhysicalAddressIndicator
					rolesNames["preferredEmailAddressIndicator"] = preferredEmailAddressIndicator
					for _, addr := range party.Addresses {
						if addr.RecordId == partyPolicyNewReferenceId {
							rolesNames["payloadNewAddressID"] = addr.RecordId

						} else if addr.RecordId == partyPolicyChangeReferenceId {
							rolesNames["payloadOldAddressID"] = addr.RecordId
						}
					}
					// For Email Validation
					rolesNames["preferredCommunicationType"] = party.PrefCommunicationType
					for _, email := range party.Emails {
						if email.EmailID == partyPolicyNewReferenceId {
							rolesNames["payloadNewEmailAddressID"] = email.EmailID

						} else if email.EmailID == partyPolicyChangeReferenceId {
							rolesNames["payloadOldEmailAddressID"] = email.EmailID
						}

					}

				}
			}
		}
	}

	if inputKey == "allPartyDataJson" {
		return rolesNames
	}

	result := rolesNames

	return result

}

func (re *RuleExecutor) getQualType(data *models.AllPolicyData, event *models.EventEnvelope, inputKey string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	var qualType string
	if data.Policy != nil && data.Policy.QualificationType != "" {
		qualType = data.Policy.QualificationType
	} else if event.DataValues != nil && event.DataValues.QualType != "" {
		qualType = event.DataValues.QualType
	}

	QualType := map[string]interface{}{
		"qualType": qualType,
	}
	result := QualType[inputKey]
	return result

}

func (re *RuleExecutor) getEventData(event *models.EventEnvelope, inputKey string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	var targetRole string
	var forcedPrint, emailNotification, eventcycledate, planCode, communicationPreference, uwrReasonCode, prefMgmtDeliveryOptionOwner, lineOfBusiness, agentType string

	for _, identifier := range event.Identifiers {
		if identifier != nil && identifier.IdentifierType == "targetRole" {
			targetRole = identifier.Value
		}
		if identifier != nil && identifier.IdentifierType == "forcedPrint" {
			forcedPrint = identifier.Value
		}
		if identifier != nil && identifier.IdentifierType == "emailNotification" {
			emailNotification = identifier.Value
		}
		if identifier != nil && identifier.IdentifierType == "originalEventTime" {
			if identifier.Value != "" {
				eventcycledate = identifier.Value
				parsedTime, _ := time.Parse(time.RFC3339, eventcycledate)
				eventcycledate = parsedTime.Format("2006-01-02")
			}
		}
		if identifier != nil && identifier.IdentifierType == "planCode" {
			planCode = identifier.Value
		}
		if identifier != nil && identifier.IdentifierType == "agentType" {
			agentType = identifier.Value
		}
		if identifier != nil && identifier.IdentifierType == "prefMgmtDeliveryOptionOwner" {
			prefMgmtDeliveryOptionOwner = identifier.Value
		}
	}
	if event.DataValues.Payload != nil {
		if event.DataValues.Payload.CoverageDetails != nil && len(event.DataValues.Payload.CoverageDetails.Owners) > 0 {
			communicationPreference = event.DataValues.Payload.CoverageDetails.Owners[0].CommunicationPreference
		}
		if event.DataValues.Payload.Underwriting != nil && len(event.DataValues.Payload.Underwriting.UnderwritingResult) > 0 {
			uwrReasonCode = event.DataValues.Payload.Underwriting.UnderwritingResult[0].ReasonCode
		}

	}
	var caseStore *models.CaseStore
	for _, casestr := range event.CaseStore {
		if casestr.EntityType == "NB_SUITABILITY_DATA" {
			caseStore = casestr
		}
	}
	if caseStore != nil && caseStore.Entity != nil && caseStore.Entity.SuitabilityRequest != nil && caseStore.Entity.SuitabilityRequest.PolicyData != nil {
		lineOfBusiness = caseStore.Entity.SuitabilityRequest.PolicyData.LineOfBusiness
	}

	EventData := map[string]interface{}{
		"partyPolicyChangeReferenceId": event.DataValues.PartyPolicyChangeReferenceId,
		"partyPolicyNewReferenceId":    event.DataValues.PartyPolicyNewReferenceId,
		"targetRole":                   targetRole,
		"forcedPrint":                  forcedPrint,
		"emailNotification":            emailNotification,
		"event":                        event.EventType,
		"cycleDate":                    eventcycledate,
		"planCode":                     planCode,
		"communicationPreference":      communicationPreference,
		"uwrReasonCode":                uwrReasonCode,
		"prefMgmtDeliveryOptionOwner":  prefMgmtDeliveryOptionOwner,
		"lineOfBusiness":               lineOfBusiness,
		"agentType":                    agentType,
	}
	result := EventData[inputKey]
	return result
}

func (re *RuleExecutor) getPartyDatapoint(policy *models.Policy, inputKey string, event *models.EventEnvelope) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)
	var beneficiaryPercentage float64
	ownerRole := utils.GetPartyRole("OWNER", policy.PartyRoles)
	ownerParty := utils.GetParty("OWNER", policy.PartyRoles, policy.Parties)
	primaryBeneficiaryRole := utils.GetPartyRole("PRIMARYBENEFICIARY", policy.PartyRoles)
	primaryBeneficiaryParty := utils.GetParty("PRIMARYBENEFICIARY", policy.PartyRoles, policy.Parties)

	var addressStatus, addressLine1, addressLine2, addressLine3, city, state, zip, zipExtension, countryCode, partyEmailAddress, partyEmailType, partyPhoneNUmber, partyPhoneType, preferredPhysicalAddressIndicator, preferredEmailAddressIndicator string
	for _, address := range ownerParty.Addresses {
		//if address.PrefAddressInd == "1" {
		addressLine1 = address.AddrLine1
		addressLine2 = address.AddrLine2
		addressLine3 = address.AddrLine3
		city = address.City
		state = address.State
		zip = address.ZipCode
		countryCode = address.AddrCountry
		zipExtension = address.ZipCodeExtension
		if address.EndDate == "" {
			addressStatus = "Active"
			preferredPhysicalAddressIndicator = address.RecordId
		} else if address.EndDate > time.Now().AddDate(0, 0, 0).Format("2006-01-02") {
			addressStatus = "Active"
			preferredPhysicalAddressIndicator = address.RecordId
		} else {
			addressStatus = "Inactive"
		}
		//}
	}
	fullName := ownerParty.FullName
	partyID := ownerParty.PartyID
	partyDOB := ownerParty.DateOfBirth
	partyAttainedAge := ownerParty.AttainedAge
	relationToInsured := ownerRole.RelationToInsured
	preferredAddressIndicator := ownerParty.PreferredAddressIndicator
	if primaryBeneficiaryParty != nil {
		beneficiaryPercentage = primaryBeneficiaryParty.BeneficiaryPercentage
	}

	partyGender := ownerParty.Gender
	partyEmail := utils.FilterEmails(ownerParty.Emails)
	for _, email := range partyEmail {
		partyEmailAddress = email.EmailAddress
		partyEmailType = email.EmailType
		if email.EndDate == "" {
			preferredEmailAddressIndicator = email.EmailID
		} else if email.EndDate >= time.Now().AddDate(0, 0, 0).Format("2006-01-02") {
			preferredEmailAddressIndicator = email.EmailID
		}
	}
	prefferedCommunicationType := ownerParty.PrefCommunicationType
	partyPhone := utils.FilterPhones(ownerParty.Phones)
	for _, phone := range partyPhone {
		partyPhoneNUmber = phone.CountryCode + phone.AreaCode + phone.DialNumber
		partyPhoneType = phone.PhoneType
	}

	partyInputs := map[string]interface{}{
		"ownerRole":                         ownerRole,
		"primaryBeneficiaryRole":            primaryBeneficiaryRole,
		"fullName":                          fullName,
		"partyID":                           partyID,
		"relationToInsured":                 relationToInsured,
		"event":                             policy.Event,
		"beneficiaryPercentage":             beneficiaryPercentage,
		"partyGender":                       partyGender,
		"prefferedCommunicationType":        prefferedCommunicationType,
		"preferredAddressIndicator":         preferredAddressIndicator,
		"partyDOB":                          partyDOB,
		"partyAttainedAge":                  partyAttainedAge,
		"addressLine1":                      addressLine1,
		"addressLine2":                      addressLine2,
		"addressLine3":                      addressLine3,
		"city":                              city,
		"state":                             state,
		"addressStatus":                     addressStatus,
		"zip":                               zip,
		"zipExtension":                      zipExtension,
		"countryCode":                       countryCode,
		"partyEmailAddres":                  partyEmailAddress,
		"partyEmailType":                    partyEmailType,
		"partyPhoneNUmber":                  partyPhoneNUmber,
		"partyPhoneType":                    partyPhoneType,
		"preferredPhysicalAddressIndicator": preferredPhysicalAddressIndicator,
		"preferredEmailAddressIndicator":    preferredEmailAddressIndicator,
	}
	if inputKey == "partyJson" {
		return partyInputs
	}

	if inputKey == "partyJson" {
		return partyInputs
	}
	if inputKey == "allPartyDataJson" {
		policyPartyInputs := re.getAllPartyDatapoint(policy, inputKey, event, preferredPhysicalAddressIndicator, preferredEmailAddressIndicator)
		return policyPartyInputs
	}
	result := partyInputs[inputKey]

	return result

}

func (re *RuleExecutor) getBankDetailsDatapoint(data *models.AllPolicyData, inputKey string, incomingEvent string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	//Refactor to remove hard coding once event type updates are done
	//Possibly move the case hard coded inputs to rules.
	var bankPartyID string
	transactionTypesToBeMapped := []string{"SystematicProgramUpdate", "FullSurrender", "FreeLookCancellation", "InitialPremium", "OneTimePremium", "LoanRepaymentOneTime"}
	if incomingEvent == config.Events.BankAccountChange.Incoming {
		bankPartyID = utils.GetRolePartyID("PAYOR", data.Policy.PartyRoles)
		if len(bankPartyID) <= 0 {
			bankPartyID = utils.GetRolePartyID("OWNER", data.Policy.PartyRoles)
		}
	} else if transactionTypesToBeMapped[0] == "SystematicProgramUpdate" {

		for _, ppdata := range data.Policy.SystematicPrograms {
			bankPartyID = utils.GetPartyIDFromBankID(ppdata.SystematicProgramParty, data.Policy.Parties)

		}
	} else {
		bankPartyID = utils.GetPartyIDFromTransactionType(data.Transactions, transactionTypesToBeMapped)
	}

	bankDetail := utils.GetBankRoleDetailsFromID(data.Policy.Parties, bankPartyID)

	if bankDetail == nil {
		return nil
	}

	bankInputs := map[string]interface{}{
		"appliesToPartyID":            bankDetail.AppliesToPartyID,
		"financialInstitutionPartyID": bankDetail.FinancialInstitutionPartyID,
		"startDate":                   bankDetail.StartDate,
		"endDate":                     bankDetail.EndDate,
		"nameOnAccount":               bankDetail.NameOnAccount,
		"branchName":                  bankDetail.BranchName,
		"accountType":                 bankDetail.AccountType,
		"accountNumber":               bankDetail.AccountNumber,
		"routingNumber":               bankDetail.RoutingNumber,
		"ibaNumber":                   bankDetail.IbaNumber,
		"accountPurpose":              bankDetail.AccountPurpose,
		"accountStatus":               bankDetail.AccountStatus,
	}

	if inputKey == "bankDetailsJson" {
		return bankInputs
	}

	result := bankInputs["bankDetailsJson"]

	return result
}

func (re *RuleExecutor) getLoanDetailsDatapoint(data *models.AllPolicyData, inputKey string, incomingEvent string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	policy := data.Policy

	loanInputs := map[string]interface{}{
		"totalLoanPrincipal":       policy.LoanValues.TotalLoanPrincipal,
		"totalLoanBalance":         policy.LoanValues.TotalLoanBalance,
		"loanPayoffAmount":         utils.CheckForNilFloat(policy.LoanValues.LoanPayoffAmt),
		"maximumLoanAmount":        policy.LoanValues.MaxLoanAmount,
		"minimumLoanAmount":        policy.LoanValues.MinLoanAmount,
		"totalLoanAccruedInterest": utils.CheckForNilFloat(policy.LoanValues.TotalLoanAccruedInterest),
		"lastLoanInterestDueDate":  policy.LoanValues.LastLoanInterestDueDate,
		"totalYearToDateLoanTaken": utils.CheckForNilFloat(policy.LoanValues.TotalYTDLoanTaken),
		"loanInterestMethod":       policy.LoanValues.LoanInterestMethod,
		"minimumLoanRepayment":     policy.LoanValues.MinLoanRepayment,
		"totalNumberOfLoan":        policy.LoanValues.TotalNumberOfLoan,
	}

	if inputKey == "loanJson" {
		return loanInputs
	}

	result := loanInputs["loanJson"]

	return result
}

func (re *RuleExecutor) getPolicyDatapoint(data *models.AllPolicyData, incomingEvent string, inputKey string, eventTime string, cycleDate string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	policy := data.Policy
	deathBenefitAmount := re.getDeathBenefitAmount(policy)
	coverageEffectiveDate := re.getCoverageEffectiveDate(policy)
	loanInterestRate := re.getInterestRate(policy)
	loanCreditRate := re.getCreditRate(policy)
	prevAnnivDate := policy.PolicyDates.PreviousPolicyAnniversaryDate
	nextAnnivDate := policy.PolicyDates.NextAnniversaryDate
	issueDate := policy.PolicyDates.IssueDate
	frequency := re.getPaymentFrequency(policy.SystematicPrograms)
	paymentStatus := re.getPaymentStatus(policy.SystematicPrograms)
	systematicPaymentAmount := re.getSystematicPaymentAmount(policy.SystematicPrograms)

	//residenceState := helpers.MapResidenceState(data.Policy.PartyRoles, data.Policy)
	metrics := data.Metrics

	var currAnnivDate string
	var currAnnivEndDate string
	var nextAnnivEndDate string
	if len(prevAnnivDate) > 0 {
		annivDates, _ := mapper.GetAnnivDates(prevAnnivDate, nextAnnivDate, issueDate)
		currAnnivDate = annivDates.CurrAnnivDate
		currAnnivEndDate = annivDates.CurrAnnivEndDate
		nextAnnivEndDate = annivDates.NextAnnivEndDate
	}
	ownerRole := utils.GetPartyRole("OWNER", policy.PartyRoles)
	ownerParty := utils.GetParty("OWNER", policy.PartyRoles, policy.Parties)
	ownerDOB, _ := time.Parse("2006-01-02", ownerParty.DateOfBirth)
	parsedTime, _ := time.Parse(time.RFC3339, cycleDate)
	cycledate := parsedTime.Format("2006-01-02")
	ownerAge := (time.Since(ownerDOB) / time.Second / 60 / 60 / 24 / 365)
	ownerAgeILLustration := ownerAge.String()
	ownerAgeILL := ownerAgeILLustration[:len(ownerAgeILLustration)-2]
	policyFeature, featureTypes := re.getPolicyFeaturesDatapoint(policy.PolicyFeatures)
	insuredRole := utils.GetPartyRole("INSURED", policy.PartyRoles)
	var issueAge *int
	var riskClass string
	if insuredRole != nil {
		issueAge = re.getIssueAge(policy, insuredRole.PartyID)
		riskClass = re.getRiskClass(policy, insuredRole.PartyID)
	} else {
		annuitantRole := utils.GetPartyRole("ANNUITANT", policy.PartyRoles)
		if annuitantRole != nil {
			issueAge = re.getIssueAge(policy, annuitantRole.PartyID)
			riskClass = re.getRiskClass(policy, annuitantRole.PartyID)
		}
	}
	fundNames := re.getPolicyFundName(policy.DistributionDetails.FundAllocationsInvestments)
	productType := policy.Product.ProductType

	policyInputs := map[string]interface{}{
		"coverageEffectiveDate":    coverageEffectiveDate,
		"currAnnivDate":            currAnnivDate,
		"currAnnivEndDate":         currAnnivEndDate,
		"deathBenefitAmount":       deathBenefitAmount,
		"endingAccountValue":       policy.PolicyValues.EndingAcctValue,
		"event":                    incomingEvent,
		"cycleDate":                cycledate, //eventTime,
		"eventStartDate":           policy.Timestamp,
		"issueAge":                 utils.CheckForNilInt(issueAge),
		"issueDate":                issueDate,
		"issueState":               policy.IssueState,
		"lineOfBusiness":           policy.Product.LineOfBusiness,
		"loanInterestRate":         loanInterestRate,
		"loanCreditRate":           loanCreditRate,
		"maturityDate":             policy.PolicyDates.MaturityDate,
		"nextAnnivDate":            nextAnnivDate,
		"nextAnnivEndDate":         nextAnnivEndDate,
		"ownerAge":                 ownerAge,
		"ownerAgeILL":              ownerAgeILL,
		"ownerRelToInsured":        ownerRole.RelationToInsured,
		"planCode":                 policy.Product.PlanCode,
		"planName":                 policy.Product.PlanName,
		"policyNumber":             policy.PolicyNumber,
		"policyStartDate":          policy.PolicyDates.PolicyStartDate,
		"policyTerm":               policy.PolicyTerm,
		"premiumAmount":            policy.PolicyValues.TotalYTDPremiumAmt,
		"frequency":                frequency,
		"productType":              productType,
		"riskClass":                &riskClass,
		"policyStatus":             policy.PolicyStatus,
		"prevAnnivDate":            prevAnnivDate,
		"productMarketingName":     policy.Product.MarketingName,
		"qualificationType":        policy.QualificationType,
		"residenceState":           policy.IssueState,
		"surrenderValue":           utils.CheckForNilFloat(policy.PolicyValues.SurrenderValue),
		"accountValueByPolicyYear": utils.CheckForNilFloat(policy.PolicyValues.AccountValueByPolicyYear),
		"totalCoverageAmount":      utils.CheckForNilFloat(policy.Coverage.TotalCoverageAmt),
		"totalLoanAmount":          policy.LoanValues.TotalLoanPrincipal,
		"totalLoanBalance":         policy.LoanValues.TotalLoanBalance,
		"totalLoanInterest":        policy.LoanValues.TotalLoanAccruedInterest,
		"totalLoanRepayment":       utils.CheckForNilFloat(policy.LoanValues.LoanPayoffAmt),
		"withdrawalAmount":         policy.WithDrawalValues.TotalYearToDateWithdrawalTaken,
		"beginningAcctValue":       policy.PolicyValues.BeginningAcctValue,
		"policyFeatures":           policy.PolicyFeatures,
		"costBasis":                policy.CostBasis.CostBasis,
		"mecStatus":                policy.PolicyValues.MecStatus,
		"featureTypes":             featureTypes,
		"metrics":                  metrics,
		"paymentStatus":            paymentStatus,
		"systematicPaymentAmount":  systematicPaymentAmount,
		"productVersion":           policy.Product.ProductVersion,
		"maximumWithdrawalAmount":  policy.WithDrawalValues.MaximumWithdrawalAmount,
		"fixedCostPeriod":          policy.FixedCostPeriod,
		"dbOptionEffectiveDate":    policy.DeathBenefit.DeathBenefitOptionEffectiveDate,
		"deathBenefitOption":       policy.DeathBenefit.DeathBenefitOption,
		"nextMonthiversaryDate":    policy.PolicyDates.NextMonthiversaryDate,
		"annualWdlLmtNCD":          policy.WithDrawalValues.AnnualWithdrawalLimitNoCoverageDecrease,
		"fundNames":                fundNames,
	}

	if inputKey == "policyJson" {
		return policyInputs
	}

	if inputKey == "policyFeaturesJson" {
		return policyFeature
	}

	if inputKey == "bankDetailsJson" {
		bankInputs := re.getBankDetailsDatapoint(data, inputKey, incomingEvent)
		return bankInputs
	}
	if inputKey == "paymentJson" {
		policyPaymentInputs := re.getPolicyPaymentDatapoint(data, data.Policy.SystematicPrograms, inputKey, incomingEvent)
		return policyPaymentInputs
	}
	if inputKey == "loanJson" {
		loanInputs := re.getLoanDetailsDatapoint(data, inputKey, incomingEvent)
		return loanInputs
	}
	// if inputKey == "allPartyDataJson" {
	// 	policyPartyInputs := re.getAllPartyDatapoint(policy, inputKey,incomingEvent)
	// 	return policyPartyInputs
	// }

	result := policyInputs[inputKey]

	return result

}

func (re *RuleExecutor) getPolicyFeaturesJsonDatapoint(policyfeatures []*models.PolicyFeature, inputKey string, event *models.EventEnvelope, policyNumber string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	policyfeaturesInput := make(map[string]interface{})

	for _, rpolicyfeatures := range policyfeatures {

		if rpolicyfeatures.FeatureType != "" {
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_featureType"] = rpolicyfeatures.FeatureType
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_paymentAmount"] = rpolicyfeatures.PaymentAmt
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_startDate"] = rpolicyfeatures.StartDate
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_endDate"] = rpolicyfeatures.EndDate
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_status"] = rpolicyfeatures.Status
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_effectiveDate"] = rpolicyfeatures.EffectiveDate
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_totalRequiredAmount"] = rpolicyfeatures.TotalRequiredAmt
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_totalMinimumRequiredAmount"] = utils.CheckForNilFloat(rpolicyfeatures.TotalMinRequiredAmt)
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_totalPaymentAmount"] = rpolicyfeatures.TotalPaymentAmt
			policyfeaturesInput[rpolicyfeatures.FeatureType+"_approvalDate"] = rpolicyfeatures.ApprovalDate
		} else {
			re.Logger.Log(log.Warning_Log, "FeatureType is missing", log.Fields{
				"CorrelationID": event.CorrelationID,
				"eventType":     event.EventType,
				"policyNumber":  policyNumber,
			})

		}

	}

	return policyfeaturesInput
}

func (re *RuleExecutor) getPolicyFeaturesDatapoint(policyFeatures []*models.PolicyFeature) (interface{}, []string) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var policyFeaturesInput []map[string]interface{}
	var featureTypes []string

	for _, policyFeature := range policyFeatures {

		featureTypes = append(featureTypes, policyFeature.FeatureType)

		policyFeaturesInputs := map[string]interface{}{
			"featureType":            policyFeature.FeatureType,
			"startDate":              policyFeature.StartDate,
			"endDate":                policyFeature.EndDate,
			"status":                 policyFeature.Status,
			"effectiveDate":          policyFeature.EffectiveDate,
			"totalRequiredAmount":    policyFeature.TotalRequiredAmt,
			"totalMinRequiredAmount": policyFeature.TotalMinRequiredAmt,
			"paymentAmount":          policyFeature.PaymentAmt,
			"totalPaymentAmount":     policyFeature.TotalPaymentAmt,
			"approvalDate":           policyFeature.ApprovalDate,
		}
		policyFeaturesInput = append(policyFeaturesInput, policyFeaturesInputs)
	}

	return policyFeaturesInput, featureTypes
}

func (re *RuleExecutor) getPolicyPreviousVersionDatapoint(policyPreviousVersion *models.Policy, inputKey string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	policyPreviousVersionInputs := map[string]interface{}{
		"totalYearToDateWithdrawalTaken": policyPreviousVersion.WithDrawalValues.TotalYearToDateWithdrawalTaken,
		"totalYearToDatePremiumAmount":   policyPreviousVersion.PolicyValues.TotalYTDPremiumAmt,
		"previousAnniversaryDate":        policyPreviousVersion.PolicyDates.PreviousPolicyAnniversaryDate,
		"prevTotalLoanRepayment":         utils.CheckForNilFloat(policyPreviousVersion.LoanValues.LoanPayoffAmt),

		"beginningSurrenderValue":       policyPreviousVersion.PolicyValues.SurrenderValue,
		"beginningCoverageAmount":       utils.CheckForNilFloat(policyPreviousVersion.Coverage.TotalCoverageAmt),
		"beginningDeathBenefitValue":    policyPreviousVersion.Coverage.CumGrossDeathBenefitAmt,
		"previousPolicyAnniversaryDate": policyPreviousVersion.PolicyDates.PreviousPolicyAnniversaryDate,
		"costBasis":                     utils.CheckForNilFloat(policyPreviousVersion.CostBasis.CostBasis),
		"mecStatus":                     policyPreviousVersion.PolicyValues.MecStatus,
		"prevTotalYearToDateLoanTaken":  utils.CheckForNilFloat(policyPreviousVersion.LoanValues.TotalYTDLoanTaken),
		"prevtotalLoanAccruedInterest":  utils.CheckForNilFloat(policyPreviousVersion.LoanValues.TotalLoanAccruedInterest),
		"policyYear":                    policyPreviousVersion.PolicyYear,
		"totalLoanRepayment":            utils.CheckForNilFloat(policyPreviousVersion.LoanValues.LoanPayoffAmt),
		"totalCoverageAmount":           utils.CheckForNilFloat(policyPreviousVersion.Coverage.TotalCoverageAmt),
	}
	//result := policyPreviousVersionInputs[inputKey]

	return policyPreviousVersionInputs
}

func (re *RuleExecutor) getproductRatesDatapoint(productRates []*models.ProductRates, inputKey string) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var productRatesInputs []map[string]interface{}

	for _, rates := range productRates {

		productRatesInput := map[string]interface{}{
			rates.RateId: rates.Rates.Ages.Age[0],
			//"age":        rates.Rates.Ages.Age[0],
		}

		productRatesInputs = append(productRatesInputs, productRatesInput)

	}
	return productRatesInputs
}

func (re *RuleExecutor) getRiskClass(policy *models.Policy, partyID string) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	coverageParticipant := helpers.GetCoverageParticipant(policy.Coverage.CoverageLayers, partyID)
	if coverageParticipant == nil {
		return ""
	}
	return coverageParticipant.RiskClass
}

func (re *RuleExecutor) getIssueAge(policy *models.Policy, partyID string) *int {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	coverageParticipant := helpers.GetCoverageParticipant(policy.Coverage.CoverageLayers, partyID)
	if coverageParticipant == nil {
		return nil
	}
	return coverageParticipant.IssueAge
}

func (re *RuleExecutor) getDeathBenefitAmount(policy *models.Policy) *float64 {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	coverageLayer := helpers.GetCoverageLayer(policy.Coverage.CoverageLayers)
	if coverageLayer == nil {
		return nil
	}

	return &coverageLayer.GrossDeathBenefitAmt
}

func (re *RuleExecutor) getPolicyFundName(funds []*models.FundAllocationsInvestments) interface{} {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var fundNames []map[string]interface{}

	if funds != nil {
		for _, fund := range funds {
			fundName := map[string]interface{}{

				"fundId":   fund.FundID,
				"fundName": fund.FundName,
			}

			fundNames = append(fundNames, fundName)
		}

		if fundNames != nil {

			return fundNames
		}
	}
	return nil
}

func (re *RuleExecutor) getPaymentFrequency(allSysPrograms []*models.SystematicProgram) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	for _, sps := range allSysPrograms {
		if sps.ArrType == "PAYMENT" && sps.Reason == "PREMIUM" {
			return sps.Frequency
		}
	}

	return ""
}

func (re *RuleExecutor) getCoverageEffectiveDate(policy *models.Policy) *string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	coverageLayer := helpers.GetCoverageLayer(policy.Coverage.CoverageLayers)
	if coverageLayer == nil {
		return nil
	}

	return &coverageLayer.CoverageEffectiveDate
}

func (re *RuleExecutor) getCreditRate(policy *models.Policy) *float64 {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	distDetails := policy.DistributionDetails
	if distDetails == nil {
		return nil
	}

	loanSegment := helpers.GetPolDistribution(distDetails)
	if loanSegment == nil {
		return nil
	}

	return &loanSegment.LoanCreditRate
}

func (re *RuleExecutor) getInterestRate(policy *models.Policy) *float64 {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	distDetails := policy.DistributionDetails
	if distDetails == nil {
		return nil
	}

	loanSegment := helpers.GetPolDistribution(distDetails)
	if loanSegment == nil {
		return nil
	}

	return &loanSegment.LoanInterestRate
}

func (re *RuleExecutor) getPaymentStatus(allSysPrograms []*models.SystematicProgram) string {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	for _, sps := range allSysPrograms {
		return sps.Status
	}

	return ""
}

func (re *RuleExecutor) getSystematicPaymentAmount(allSysPrograms []*models.SystematicProgram) float64 {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	if len(allSysPrograms) > 0 {
		return allSysPrograms[0].Amount
	}

	return 0
}

func GetRulesByLetter(allRules []*models.RuleConfig, letterRuleConfig map[string][]string) RulesByLetterCfg {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	rulesByLetter := RulesByLetterCfg{}

	for letterType, ruleIDs := range letterRuleConfig {
		var letterRules []*models.RuleConfig

		for _, ruleID := range ruleIDs {
			for _, rule := range allRules {
				if rule.ID == ruleID {
					letterRules = append(letterRules, rule)
					break
				}
			}
		}

		rulesByLetter[letterType] = letterRules
	}
	return rulesByLetter
}

func GetRulesConfig(configDir string) ([]*models.RuleConfig, error) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	var rulesConfig []*models.RuleConfig

	filenames, err := os.ReadDir(configDir)
	if err != nil {
		return nil, err
	}

	for _, f := range filenames {
		path := configDir + "/" + f.Name()
		content, err := os.ReadFile(path)
		if err != nil {
			return nil, err
		}

		var rule *models.RuleConfig
		err = json.Unmarshal(content, &rule)
		if err != nil {
			return nil, err
		}

		rulesConfig = append(rulesConfig, rule)
	}

	return rulesConfig, nil
}

func (re *RuleExecutor) setFundNames(ruleOutput []models.RuleOutput, data []*models.Fund) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if len(output.FundNameMap) != 0 {

			for _, fund := range data {
				for _, newName := range output.FundNameMap {
					tagValues := strings.Split(newName.FundNameMap, ":")
					if fund.FundAccountName == tagValues[0] {
						fund.FundAccountName = html.UnescapeString(tagValues[1])
					}
				}

			}
		}
	}

}

func (re *RuleExecutor) setFundIndexNames(ruleOutput []models.RuleOutput, data []*models.Fund) {
	ctx = context.WithValue(ctx, "CorrelationID", "")
	defer utils.PanicHandler(ctx)
	for _, output := range ruleOutput {

		for _, fundType := range output.FundAccountTypes {

			for _, fund := range data {

				tagValues := strings.Split(fundType.FundAccountType, ":")
				if fund.FundAccountType == tagValues[0] {

					fund.FundIndexName = html.UnescapeString(tagValues[1])
				}

			}
		}
	}
}

func (re *RuleExecutor) setEmailAddressIndicator(ruleOutput []models.RuleOutput, data *models.AllPolicyData, event *models.EventEnvelope, ruleName string) {
	ctx = context.WithValue(ctx, "CorrelationID", event.CorrelationID)
	defer utils.PanicHandler(ctx)

	for _, output := range ruleOutput {
		if len(output.EmailAddressId) != 0 {
			for _, role := range data.Policy.PartyRoles {
				if role.PartyRole == output.TargetRole {
					for _, party := range data.Policy.Parties {
						if role.PartyID == party.PartyID {
							for _, paddr := range party.Emails {
								if paddr.EmailID == output.EmailAddressId {
									paddr.EmailIndicator = output.EmailAddressIndicator
								}

							}
						}
					}

				}
			}
		}
	}
}
