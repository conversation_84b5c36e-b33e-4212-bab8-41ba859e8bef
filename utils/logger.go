package utils

import (
	"context"
	"correspondence-composer/utils/log"
	"sync"
)

var (
	appLogger     log.Logger
	loggerOnce    sync.Once
	loggerMutex   sync.RWMutex
)

// GetLogger returns a singleton logger instance
func GetLogger() log.Logger {
	loggerOnce.Do(func() {
		appLogger = log.New(log.Config{
			ServiceName: SERVICE_NAME,
		})
	})
	
	return appLogger
}

// SetLoggerInContext adds the logger to the provided context
func SetLoggerInContext(ctx context.Context, correlationID string) context.Context {
	logger := GetLogger()
	env := GetConfigUtils().Environment
	
	ctx = context.WithValue(ctx, "logger", logger)
	ctx = context.WithValue(ctx, "CorrelationID", correlationID)
	ctx = context.WithValue(ctx, "env", env)
	
	return ctx
}