package mapper

import (
	"correspondence-composer/models"
	"correspondence-composer/utils"
	"fmt"
	"strconv"

	"context"
	xmlgenmodels "correspondence-composer/models/generated"
)

var (
	ctx = context.Background()
)

func MapBasePolicyDataToXML(policyData *models.Policy,
	carrierData *models.Carrier,
	correlationID string,
	ruleResults *models.RulesResults,
	tdata []*models.Transaction,
	transTypesToBeMapped []string,
	event *models.EventEnvelope,
	mcsAgent []*models.McsAgent) *xmlgenmodels.POLICY {
	ctx = context.WithValue(ctx, "CorrelationID", correlationID)
	defer utils.PanicHandler(ctx)
	basePolicy := &xmlgenmodels.POLICY{
		POLPOLNUM:               policyData.PolicyNumber,
		POLCONT:                 policyData.PolicyNumber,
		POLQUALTYPE:             policyData.QualificationType,
		POLQUALDESC:             policyData.QualificationType,
		POLPLANCODE:             policyData.Product.PlanCode,
		POLPRCSGCOMP:            carrierData.CarrierBusinessName,
		POLPRODUCT:              policyData.Product.PlanName,
		POLTRACKINGID:           correlationID,
		POLPRODSHORTNAME:        policyData.Product.ShortName,
		POLPRDCTMKTGNAME:        policyData.Product.MarketingName,
		POLISSUESTATE:           policyData.IssueState,
		POLISSUEDATE:            policyData.PolicyDates.IssueDate,
		POLISSUETYPE:            policyData.IssueType,
		POLSTARTDATE:            policyData.PolicyDates.PolicyStartDate,
		POLMATURITYDATE:         policyData.PolicyDates.MaturityDate,
		POLTERMDATE:             policyData.PolicyDates.PolicyTerminationDate,
		POLRESIDENCESTATE:       policyData.IssueState, // MapResidenceState(policyData.PartyRoles, policyData), //policyData.ResidenceState,
		POLTERM:                 policyData.PolicyTerm,
		POLLOB:                  policyData.Product.LineOfBusiness,
		POLCURRYR:               policyData.PolicyYear,
		POLOWNERSAMEASINS:       "N",
		POLMONTHLYDEDUCTIONDATE: policyData.PolicyDates.NextMonthiversaryDate,
		POLANNSTARTDATE:         policyData.PolicyDates.MaturityDate,
		POLACCOUNTVALUE:         strconv.FormatFloat(policyData.PolicyValues.EndingAcctValue, 'f', 2, 64),
		PARTIES:                 MapPartiesToPolicy(policyData, ruleResults, event, mcsAgent),
		CARRIER:                 MapCarrierDataToPolicy(carrierData, policyData, ruleResults),

		//EVENTS:               MapEventsToPolicy(policyData),
		POLICYTRANSACTIONS:   MapTransactionsToPolicy(tdata, nil, ruleResults.FundId),
		POLICYVALUES:         MapPolicyValuesToPolicy(nil, nil),
		POLICYLOANS:          MapLoanValuesToPolicy(policyData, nil, nil),
		POLICYRIDERS:         MapRidersToPolicy(policyData, nil, nil, nil),
		POLICYFUNDS:          MapPolicyFundsToPolicy(policyData),
		FUNDS:                MapFundsToPolicy(tdata, policyData),
		POLICYPREMIUMS:       MapPremiumToPolicy(policyData),
		POLICYPAYMENTS:       MapPolicyPaymentsToPolicy(policyData, ruleResults.ArrangementTypeToMap),
		FEATURES:             MapPolicyFeaturesToPolicy(policyData),
		TRANSACTIONSUMMARIES: MapTransactionSummariesToPolicy(tdata, policyData),

		//ILLUSTRATIONVALUES: MapIllustrationValuesToPolicy(policyData),

		// The empty values below will be populated via rules
		RECTYPE:             "",
		POLPRODCODE:         "",
		POLPRDCTCOMP:        "",
		POLDOCTYPE:          "",
		POLSERVICENAME:      "",
		POLSERVICEDESC:      "",
		POLSYSCODE:          "",
		POLSOURCE:           "",
		POLPRODUCTTYPE:      "",
		POLRISKCLASS:        "",
		POLPREMMODE:         "",
		POLPAYMENTFREQUENCY: "",
		POLTAXYEAR:          "",
		POLTAXDOCUMENTS:     "",
		POLROLETYPE:         "",
	}

	if policyData.PolicyDates != nil && len(policyData.PolicyDates.NextAnniversaryDate) > 0 {
		basePolicy.POLCURRANNIVDATE = policyData.PolicyDates.NextAnniversaryDate
	}

	sameAsInsured := OwnerSameAsInsured(policyData.PartyRoles)
	if sameAsInsured {
		basePolicy.POLOWNERSAMEASINS = "Y"
	}

	ownerRole := utils.GetPartyRole("OWNER", policyData.PartyRoles)

	coverageParticipant := GetCoverageParticipant(policyData.Coverage.CoverageLayers, ownerRole.PartyID)
	if coverageParticipant != nil {
		basePolicy.POLISSUEAGE = utils.SetIntNilToZero(coverageParticipant.IssueAge).(int)
	}

	var xmlPolicyBanks []*xmlgenmodels.POLICYBANK
	var partyID string
	var err error
	if len(transTypesToBeMapped) == 0 {
		partyID, err = utils.GetRolePartyIDForXML("PAYOR", policyData.PartyRoles)
		if len(partyID) <= 0 {
			partyID, err = utils.GetRolePartyIDForXML("OWNER", policyData.PartyRoles)
		}
		if err != nil {
			return basePolicy
		}
	} else {
		for _, ttype := range transTypesToBeMapped {
			switch ttype {
			case "FullSurrender", "FreeLookCancellation":
				partyID = utils.GetFullSurrenderRole(tdata)
			case "SystematicProgramUpdate":
				for _, ppdata := range policyData.SystematicPrograms {
					partyID = utils.GetPartyIDFromBankID(ppdata.SystematicProgramParty, policyData.Parties)
				}

			default:
				partyID, err = utils.GetRolePartyIDForXML("PAYOR", policyData.PartyRoles)
				if err != nil {
					return basePolicy
				}
			}
		}
	}
	payorBankInfo := MapBankInfoToParty(partyID, policyData.Parties)
	xmlPolicyBanks = append(xmlPolicyBanks, payorBankInfo)
	if len(xmlPolicyBanks) > 0 {
		basePolicy.POLICYBANKINFO = &xmlgenmodels.POLICYBANKINFO{
			POLICYBANK: xmlPolicyBanks,
		}
	}

	//POL_SURRENDER_DATE will generate for each transaction types ..valid requirement is only for fullsurrender
	for _, ptrans := range tdata {
		if containsTransType(transTypesToBeMapped, ptrans.TransactionType) && len(transTypesToBeMapped) > 0 {

			basePolicy.POLSURRENDERDATE = ptrans.ProcessDate
		}
	}
	if len(policyData.SystematicPrograms) > 0 {
		for _, sps := range policyData.SystematicPrograms {
			if sps.ArrType == "PAYOUT" {
				basePolicy.POLWITHHOLDINGAMOUNT = strconv.FormatFloat(sps.Amount, 'f', 2, 64)
				basePolicy.POLPAYMENTFREQUENCY = sps.Frequency //this tag is only for Arcus Payout
			}
		}
	}

	if event != nil {
		if len(event.Identifiers) > 0 {
			for _, identifier := range event.Identifiers {
				switch identifier.IdentifierType {
				case "taxYear":
					if identifier.Value != "" {
						basePolicy.POLTAXYEAR = identifier.Value
					}
				case "documents":
					if identifier.Value != "" {
						basePolicy.POLTAXDOCUMENTS = identifier.Value
					}
				}
			}
		}

	}

	fullName := retrieveFullName(policyData, ruleResults.POLROLETYPE)
	if len(fullName) > 0 {
		basePolicy.POLFULLNAME = fullName
	}
	return basePolicy
}

func retrieveFullName(policyData *models.Policy, policyRoleType string) string {
	partyId := ""
	fullName := ""
	for _, role := range policyData.PartyRoles {
		if role.PartyRole == policyRoleType {
			partyId = role.PartyID
		}
	}
	for _, party := range policyData.Parties {
		if party.PartyID == partyId {
			fullName = fmt.Sprintf("%s %s", party.FirstName, party.LastName)
		}
	}
	return fullName
}
