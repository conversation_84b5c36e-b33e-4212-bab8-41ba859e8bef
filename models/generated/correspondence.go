// Code generated by xgen. DO NOT EDIT.

package schema

import (
	"encoding/xml"
)

// AlphanumericString ...
type AlphanumericString string

// PhoneNumber ...
type PhoneNumber string

// Email ...
type Email string

// OptionalDate ...
type OptionalDate string

// POLAMENDMENT ...
type POLAMENDMENT struct {
	XMLName          xml.Name `xml:"POL_AMENDMENT"`
	POLAMENDMENTCODE string   `xml:"POL_AMENDMENT_CODE"`
	POLAMENDMENTDESC string   `xml:"POL_AMENDMENT_DESC"`
}

// POLAMENDMENTS ...
type POLAMENDMENTS struct {
	XMLName      xml.Name        `xml:"POL_AMENDMENTS"`
	POLAMENDMENT []*POLAMENDMENT `xml:"POL_AMENDMENT"`
}

// DOCUMENT ...
type DOCUMENT struct {
	DOCUMENTNAME1  string `xml:"DOCUMENT_NAME_1"`
	DOCUMENTNAME2  string `xml:"DOCUMENT_NAME_2"`
	DOCUMENTNAME3  string `xml:"DOCUMENT_NAME_3"`
	DOCUMENTNAME4  string `xml:"DOCUMENT_NAME_4"`
	DOCUMENTNAME5  string `xml:"DOCUMENT_NAME_5"`
	DOCUMENTNAME6  string `xml:"DOCUMENT_NAME_6"`
	DOCUMENTNAME7  string `xml:"DOCUMENT_NAME_7"`
	DOCUMENTNAME8  string `xml:"DOCUMENT_NAME_8"`
	DOCUMENTNAME9  string `xml:"DOCUMENT_NAME_9"`
	DOCUMENTNAME10 string `xml:"DOCUMENT_NAME_10"`
}

// DOCUMENTS ...
type DOCUMENTS struct {
	DOCUMENT []*DOCUMENT `xml:"DOCUMENT"`
}

// DELIVERY ...
type DELIVERY struct {
	RELATEDROLEID                  string `xml:"RELATED_ROLE_ID"`
	ROLEOPTID                      int    `xml:"ROLE_OPT_ID"`
	PARTYDELIVERYIND               string `xml:"PARTY_DELIVERY_IND"`
	PARTYDELIVERYTYPE              string `xml:"PARTY_DELIVERY_TYPE"`
	PARTYDELIVERYEMAILNOTIFICATION string `xml:"PARTY_DELIVERY_EMAIL_NOTIFICATION"`
}

// ROLE ...
type ROLE struct {
	ROLEID        string `xml:"ROLE_ID"`
	ROLECODE      string `xml:"ROLE_CODE"`
	ROLETYPE      string `xml:"ROLE_TYPE"`
	ROLENAME      string `xml:"ROLE_NAME"`
	ROLESTATUS    string `xml:"ROLE_STATUS"`
	RELATIONTOINS string `xml:"RELATION_TO_INS"`
	ROLECCIND     string `xml:"ROLE_CC_IND"`
}

// ROLES ...
type ROLES struct {
	ROLE []*ROLE `xml:"ROLE"`
}

// ADDRESS ...
type ADDRESS struct {
	ADDRTYPE           string `xml:"ADDR_TYPE"`
	ADDRLINE1          string `xml:"ADDR_LINE1"`
	ADDRLINE2          string `xml:"ADDR_LINE2"`
	ADDRLINE3          string `xml:"ADDR_LINE3"`
	ADDRCITY           string `xml:"ADDR_CITY"`
	ADDRSTATE          string `xml:"ADDR_STATE"`
	ADDRZIP            string `xml:"ADDR_ZIP"`
	ADDRZIPEXTN        string `xml:"ADDR_ZIP_EXTN"`
	ADDRCNTRYCODE      string `xml:"ADDR_CNTRY_CODE"`
	ADDRSTATUS         string `xml:"ADDR_STATUS"`
	ADDRPREFIND        string `xml:"ADDR_PREF_IND"`
	ADDRCLASSIFICATION string `xml:"ADDR_CLASSIFICATION"`
}

// ADDRESSES ...
type ADDRESSES struct {
	ADDRESS []*ADDRESS `xml:"ADDRESS"`
}

// EMAILADDRESS ...
type EMAILADDRESS struct {
	XMLName             xml.Name `xml:"EMAIL_ADDRESS"`
	EMAILADDR           string   `xml:"EMAIL_ADDR"`
	EMAILTYPE           string   `xml:"EMAIL_TYPE"`
	STATUS              string   `xml:"STATUS"`
	EMAILPREFERREDIND   string   `xml:"EMAIL_PREFERRED_IND"`
	EMAILCLASSIFICATION string   `xml:"EMAIL_CLASSIFICATION"`
}

// EMAILADDRESSES ...
type EMAILADDRESSES struct {
	XMLName      xml.Name        `xml:"EMAIL_ADDRESSES"`
	EMAILADDRESS []*EMAILADDRESS `xml:"EMAIL_ADDRESS"`
}

// PARTYPHONE ...
type PARTYPHONE struct {
	XMLName           xml.Name `xml:"PARTY_PHONE"`
	PHNNUM            string   `xml:"PHN_NUM"`
	PHNEXT            string   `xml:"PHN_EXT"`
	PHNTYPE           string   `xml:"PHN_TYPE"`
	STATUS            string   `xml:"STATUS"`
	PHNPREFERREDIND   string   `xml:"PHN_PREFERRED_IND"`
	PHNCLASSIFICATION string   `xml:"PHN_CLASSIFICATION"`
}

// PARTYPHONES ...
type PARTYPHONES struct {
	XMLName    xml.Name      `xml:"PARTY_PHONES"`
	PARTYPHONE []*PARTYPHONE `xml:"PARTY_PHONE"`
}

// PARTY ...
type PARTY struct {
	PARTYCONT        string          `xml:"PARTY_CONT"`
	PARTYID          string          `xml:"PARTY_ID"`
	PARTYPCT         float64         `xml:"PARTY_PCT"`
	PARTYTAXID       string          `xml:"PARTY_TAX_ID"`
	PARTYEXTERNALID  string          `xml:"PARTY_EXTERNAL_ID"`
	PARTYAPPROVALIND string          `xml:"PARTY_APPROVAL_IND"`
	DELIVERY         *DELIVERY       `xml:"DELIVERY"`
	ROLES            *ROLES          `xml:"ROLES"`
	PARTYTYPE        string          `xml:"PARTY_TYPE"`
	PARTYFULLNAME    string          `xml:"PARTY_FULL_NAME"`
	PARTYPREFIX      string          `xml:"PARTY_PREFIX"`
	PARTYFSTNAME     string          `xml:"PARTY_FST_NAME"`
	PARTYMI          string          `xml:"PARTY_MI"`
	PARTYLSTNAME     string          `xml:"PARTY_LST_NAME"`
	PARTYSUFFIX      string          `xml:"PARTY_SUFFIX"`
	ADDRESSES        *ADDRESSES      `xml:"ADDRESSES"`
	PARTYDOB         string          `xml:"PARTY_DOB"`
	PARTYATTAINAGE   int             `xml:"PARTY_ATTAIN_AGE"`
	PARTYGENDER      string          `xml:"PARTY_GENDER"`
	EMAILADDRESSES   *EMAILADDRESSES `xml:"EMAIL_ADDRESSES"`
	PARTYPHONES      *PARTYPHONES    `xml:"PARTY_PHONES"`
	PREFERRED        string          `xml:"PREFERRED"`
}

// PARTIES ...
type PARTIES struct {
	PARTY []*PARTY `xml:"PARTY"`
}

// CARRIERADDRESS ...
type CARRIERADDRESS struct {
	XMLName                xml.Name `xml:"CARRIER_ADDRESS"`
	CARRIERADDRLINE1       string   `xml:"CARRIER_ADDR_LINE1"`
	CARRIERADDRLINE2       string   `xml:"CARRIER_ADDR_LINE2"`
	CARRIERADDRLINE3       string   `xml:"CARRIER_ADDR_LINE3"`
	CARRIERCITY            string   `xml:"CARRIER_CITY"`
	CARRIERSTATE           string   `xml:"CARRIER_STATE"`
	CARRIERZIP             string   `xml:"CARRIER_ZIP"`
	CARRIERZIPEXTN         string   `xml:"CARRIER_ZIP_EXTN"`
	CARRIERADDRCOUNTRYCODE string   `xml:"CARRIER_ADDR_COUNTRY_CODE"`
	CARRIERADDRTYPE        string   `xml:"CARRIER_ADDR_TYPE"`
	CARRIERADDRSTATUS      string   `xml:"CARRIER_ADDR_STATUS"`
}

// CARRIERADDRESSES ...
type CARRIERADDRESSES struct {
	XMLName        xml.Name          `xml:"CARRIER_ADDRESSES"`
	CARRIERADDRESS []*CARRIERADDRESS `xml:"CARRIER_ADDRESS"`
}

// CARRIERPHONE ...
type CARRIERPHONE struct {
	XMLName        xml.Name `xml:"CARRIER_PHONE"`
	CARRIERPHNNUM  string   `xml:"CARRIER_PHN_NUM"`
	CARRIERPHNTYPE string   `xml:"CARRIER_PHN_TYPE"`
}

// CARRIERPHONES ...
type CARRIERPHONES struct {
	XMLName      xml.Name        `xml:"CARRIER_PHONES"`
	CARRIERPHONE []*CARRIERPHONE `xml:"CARRIER_PHONE"`
}

// CARRIEREMAIL ...
type CARRIEREMAIL struct {
	XMLName          xml.Name `xml:"CARRIER_EMAIL"`
	CARRIEREMAILADDR string   `xml:"CARRIER_EMAIL_ADDR"`
	CARRIEREMAILTYPE string   `xml:"CARRIER_EMAIL_TYPE"`
}

// CARRIEREMAILS ...
type CARRIEREMAILS struct {
	XMLName      xml.Name        `xml:"CARRIER_EMAILS"`
	CARRIEREMAIL []*CARRIEREMAIL `xml:"CARRIER_EMAIL"`
}

// CARRIERWEBLINK ...
type CARRIERWEBLINK struct {
	XMLName            xml.Name `xml:"CARRIER_WEB_LINK"`
	CARRIERWEBLINKURL  string   `xml:"CARRIER_WEB_LINK_URL"`
	CUSTOMERWEBLINKURL string   `xml:"CUSTOMER_WEB_LINK_URL"`
	AGENTWEBLINKURL    string   `xml:"AGENT_WEB_LINK_URL"`
	CARRIERWEBLINKTYPE string   `xml:"CARRIER_WEB_LINK_TYPE"`
}

// CARRIERWEBLINKS ...
type CARRIERWEBLINKS struct {
	XMLName        xml.Name          `xml:"CARRIER_WEB_LINKS"`
	CARRIERWEBLINK []*CARRIERWEBLINK `xml:"CARRIER_WEB_LINK"`
}

// CARRIER ...
type CARRIER struct {
	CARRIERCONT         string            `xml:"CARRIER_CONT"`
	CARRIERID           string            `xml:"CARRIER_ID"`
	CARRIERCODE         string            `xml:"CARRIER_CODE"`
	CARRIERNAICCODE     string            `xml:"CARRIER_NAIC_CODE"`
	CARRIERORGCODE      string            `xml:"CARRIER_ORG_CODE"`
	CARRIERDISPLAYNAME  string            `xml:"CARRIER_DISPLAY_NAME"`
	CARRIERBUSINESSNAME string            `xml:"CARRIER_BUSINESS_NAME"`
	CARRIERADDRESSES    *CARRIERADDRESSES `xml:"CARRIER_ADDRESSES"`
	CARRIERPHONES       *CARRIERPHONES    `xml:"CARRIER_PHONES"`
	CARRIEREMAILS       *CARRIEREMAILS    `xml:"CARRIER_EMAILS"`
	CARRIERWEBLINKS     *CARRIERWEBLINKS  `xml:"CARRIER_WEB_LINKS"`
	CARRIEROFFICEHOURS  string            `xml:"CARRIER_OFFICE_HOURS"`
	CARRIEROFFICEDAYS   string            `xml:"CARRIER_OFFICE_DAYS"`
	CARRIERBUDGETCNTR   string            `xml:"CARRIER_BUDGET_CNTR"`
}

// CARRIERINFORMATION ...
type CARRIERINFORMATION struct {
	XMLName xml.Name `xml:"CARRIER_INFORMATION"`
	CARRIER *CARRIER `xml:"CARRIER"`
}

// EXCHANGE ...
type EXCHANGE struct {
	EXCROLECODE        string  `xml:"EXC_ROLE_CODE"`
	EXCTRNSCOMPACCTNUM string  `xml:"EXC_TRNS_COMP_ACCT_NUM"`
	EXCCOMPACCTNUM     string  `xml:"EXC_COMP_ACCT_NUM"`
	EXCCOMPNAME        string  `xml:"EXC_COMP_NAME"`
	EXCCOMPADDRLINE1   string  `xml:"EXC_COMP_ADDR_LINE1"`
	EXCCOMPADDRLINE2   string  `xml:"EXC_COMP_ADDR_LINE2"`
	EXCCOMPADDRLINE3   string  `xml:"EXC_COMP_ADDR_LINE3"`
	EXCCOMPCITY        string  `xml:"EXC_COMP_CITY"`
	EXCCOMPSTATE       string  `xml:"EXC_COMP_STATE"`
	EXCCOMPZIP         string  `xml:"EXC_COMP_ZIP"`
	EXCDELIVERYMODE    string  `xml:"EXC_DELIVERY_MODE"`
	EXCFAXNUM          string  `xml:"EXC_FAX_NUM"`
	EXCERNUM           string  `xml:"EXC_ER_NUM"`
	EXCTOTAMT          float64 `xml:"EXC_TOT_AMT"`
}

// EXCHANGES ...
type EXCHANGES struct {
	EXCHANGE *EXCHANGE `xml:"EXCHANGE"`
}

// FINANCIALINFORMATION ...
type FINANCIALINFORMATION struct {
	XMLName                        xml.Name `xml:"FINANCIAL_INFORMATION"`
	FINANCIALANNUALINCOME          string   `xml:"FINANCIAL_ANNUAL_INCOME"`
	FINANCIALANNUALEXPENSES        string   `xml:"FINANCIAL_ANNUAL_EXPENSES"`
	FINANCIALDISPOSABLEINCOME      string   `xml:"FINANCIAL_DISPOSABLE_INCOME"`
	FINANCIALCHANGEACKNOWLEDGEMENT string   `xml:"FINANCIAL_CHANGE_ACKNOWLEDGEMENT"`
	FINANCIALRESIDENCETYPE         string   `xml:"FINANCIAL_RESIDENCE_TYPE"`
	FINANCIALSUFFICIENTFUND        string   `xml:"FINANCIAL_SUFFICIENT_FUND"`
	FINANCIALTAXBRACKET            string   `xml:"FINANCIAL_TAX_BRACKET"`
	FINANCIALNETWORTH              string   `xml:"FINANCIAL_NET_WORTH"`
	FINANCIALLIQUIDASSET           string   `xml:"FINANCIAL_LIQUID_ASSET"`
	FINANCIALCABENEFIT             string   `xml:"FINANCIAL_CABENEFIT"`
	FINANCIALPRODUCTS              string   `xml:"FINANCIAL_PRODUCTS"`
	FINANCIALTOTALANNUITYVALUE     string   `xml:"FINANCIAL_TOTAL_ANNUITY_VALUE"`
	FINANCIALPOTENTIALINTERESTS    string   `xml:"FINANCIAL_POTENTIAL_INTERESTS"`
	FINANCIALSOURCEOFFUNDS         string   `xml:"FINANCIAL_SOURCE_OF_FUNDS"`
	FINANCIALREPLACEMENTS          string   `xml:"FINANCIAL_REPLACEMENTS"`
	FINANCIALDISTRIBUTIONS         string   `xml:"FINANCIAL_DISTRIBUTIONS"`
	FINANCIALFIRSTDISTRIBUTION     string   `xml:"FINANCIAL_FIRST_DISTRIBUTION"`
}

// POLICYVALUES ...
type POLICYVALUES struct {
	PVALCONT                                string `xml:"PVAL_CONT"`
	PVALBEGINDATE                           string `xml:"PVAL_BEGIN_DATE"`
	PVALENDDATE                             string `xml:"PVAL_END_DATE"`
	PVALBEGINCOVERVAL                       string `xml:"PVAL_BEGIN_COVER_VAL"`
	PVALENDCOVERVAL                         string `xml:"PVAL_END_COVER_VAL"`
	PVALCHGCOVERVAL                         string `xml:"PVAL_CHG_COVER_VAL"`
	PVALBEGINDEATHBNFTVAL                   string `xml:"PVAL_BEGIN_DEATH_BNFT_VAL"`
	PVALENDDEATHBNFTVAL                     string `xml:"PVAL_END_DEATH_BNFT_VAL"`
	PVALCHGDEATHBNFTVAL                     string `xml:"PVAL_CHG_DEATH_BNFT_VAL"`
	PVALBEGINSURRVAL                        string `xml:"PVAL_BEGIN_SURR_VAL"`
	PVALENDSURRVAL                          string `xml:"PVAL_END_SURR_VAL"`
	PVALCHGSURRVAL                          string `xml:"PVAL_CHG_SURR_VAL"`
	PVALBEGINACCVAL                         string `xml:"PVAL_BEGIN_ACC_VAL"`
	PVALENDACCVAL                           string `xml:"PVAL_END_ACC_VAL"`
	PVALCHGACCVAL                           string `xml:"PVAL_CHG_ACC_VAL"`
	PVALTOTALPAYRCVD                        string `xml:"PVAL_TOTAL_PAY_RCVD"`
	PVALTOTALCOICOST                        string `xml:"PVAL_TOTAL_COI_COST"`
	PVALCOSTOFINSVAL                        string `xml:"PVAL_COST_OF_INS_VAL"`
	PVALTOTALUNITCHARGE                     string `xml:"PVAL_TOTAL_UNIT_CHARGE"`
	PVALTOTALEXPENSECHARGE                  string `xml:"PVAL_TOTAL_EXPENSE_CHARGE"`
	PVALTOTALPARTWITHDRAWVAL                string `xml:"PVAL_TOTAL_PART_WITHDRAW_VAL"`
	PVALTOTALLOANAMT                        string `xml:"PVAL_TOTAL_LOAN_AMT"`
	PVALTOTALLOANBALNAMT                    string `xml:"PVAL_TOTAL_LOAN_BALN_AMT"`
	PVALTOTALLOANREPAYAMT                   string `xml:"PVAL_TOTAL_LOAN_REPAY_AMT"`
	PVALTOTALINTCHARGE                      string `xml:"PVAL_TOTAL_INT_CHARGE"`
	PVALTOTALINTCREDIT                      string `xml:"PVAL_TOTAL_INT_CREDIT"`
	PVALINTCREDITCURRRATE                   string `xml:"PVAL_INT_CREDIT_CURR_RATE"`
	PVALLOANINTCURRRATE                     string `xml:"PVAL_LOAN_INT_CURR_RATE"`
	PVALLOANCREDITCURRRATE                  string `xml:"PVAL_LOAN_CREDIT_CURR_RATE"`
	PVALINTCREDITNEXTYRRATE                 string `xml:"PVAL_INT_CREDIT_NEXT_YR_RATE"`
	PVALLOANINTNEXTYRRATE                   string `xml:"PVAL_LOAN_INT_NEXT_YR_RATE"`
	PVALLOANCREDITNEXTYRRATE                string `xml:"PVAL_LOAN_CREDIT_NEXT_YR_RATE"`
	PVALCURRPAYAMT                          string `xml:"PVAL_CURR_PAY_AMT"`
	PVALCURRPAYMODE                         string `xml:"PVAL_CURR_PAY_MODE"`
	PVALANNLPSAMT                           string `xml:"PVAL_ANN_LPS_AMT"`
	PVALINITIALPREMIUMREQUESTAMOUNT         string `xml:"PVAL_INITIAL_PREMIUM_REQUEST_AMOUNT"`
	PVALINITIALPAYMENTAMOUNTRECEIVEDDATE    string `xml:"PVAL_INITIAL_PAYMENT_AMOUNT_RECEIVED_DATE"`
	PVALGUARANTEEDINTERESTRATE              string `xml:"PVAL_GUARANTEED_INTEREST_RATE"`
	PVALFIXEDGUARANTEEDMINIINTERESTRATE     string `xml:"PVAL_FIXED_GUARANTEED_MINI_INTEREST_RATE"`
	PVALGUARANTEEDMONTHLYEXPENSECHARGE      string `xml:"PVAL_GUARANTEED_MONTHLY_EXPENSE_CHARGE"`
	PVALGUARANTEEDMONTHLYPOSTEXPENSECHARGE  string `xml:"PVAL_GUARANTEED_MONTHLY_POST_EXPENSE_CHARGE"`
	PVALGUARANTEEDMONTHLYUNITCHARGERATE     string `xml:"PVAL_GUARANTEED_MONTHLY_UNIT_CHARGE_RATE"`
	PVALGUARANTEEDMONTHLYPOSTUNITCHARGERATE string `xml:"PVAL_GUARANTEED_MONTHLY_POST_UNIT_CHARGE_RATE"`
	PVALPAYMENTCHARGEPERCENTAGE             string `xml:"PVAL_PAYMENT_CHARGE_PERCENTAGE"`
	PVALMINREQUIREDPREMIUMAMOUNT            string `xml:"PVAL_MIN_REQUIRED_PREMIUM_AMOUNT"`
	PVALINITIALPREMIUMRECEIVEDAMOUNT        string `xml:"PVAL_INITIAL_PREMIUM_RECEIVED_AMOUNT"`
	PVALINITIALINTRATE                      string `xml:"PVAL_INITIAL_INT_RATE"`
	WITHDRAWALMAXANNUALPERCENTAGE           string `xml:"WITHDRAWAL_MAX_ANNUAL_PERCENTAGE"`
	GAURSUBSEQPERCENTAGE                    string `xml:"GAUR_SUBSEQ_PERCENTAGE"`
	GAURINITIALPERCENTAGE                   string `xml:"GAUR_INITIAL_PERCENTAGE"`
	PVALGUARMININTRATE                      string `xml:"PVAL_GUAR_MIN_INT_RATE"`
	PVALMININITPCT                          string `xml:"PVAL_MIN_INIT_PCT"`
	PVALMINSUBPCT                           string `xml:"PVAL_MIN_SUB_PCT"`
	PVALGUARMINRATE                         string `xml:"PVAL_GUAR_MIN_RATE"`
	PVALGUARSURRPERCENTAGE                  string `xml:"PVAL_GUAR_SURR_PERCENTAGE"`
	PVALNFFORFEITURERATE                    string `xml:"PVAL_NF_FORFEITURE_RATE"`
	PVALWITHDRAWALFREEPCT                   string `xml:"PVAL_WITHDRAWAL_FREE_PCT"`
	PVALMVAINDEX                            string `xml:"PVAL_MVA_INDEX"`
	PVALWITHDRAWALMINAMT                    string `xml:"PVAL_WITHDRAWAL_MIN_AMT"`
	PVALCONTRACTMINVAL                      string `xml:"PVAL_CONTRACT_MIN_VAL"`
	PVALSURRCHRGTERMPERC                    string `xml:"PVAL_SURR_CHRG_TERM_PERC"`
}

// SURRENDERCHARGE ...
type SURRENDERCHARGE struct {
	SURRCHRGTYPE     string `xml:"SURR_CHRG_TYPE"`
	SURRCHRGTERMPERC string `xml:"SURR_CHRG_TERM_PERC"`
	SURRCHRGTERMYEAR int    `xml:"SURR_CHRG_TERM_YEAR"`
}

// SURRENDERCHARGES ...
type SURRENDERCHARGES struct {
	SURRENDERCHARGE []*SURRENDERCHARGE `xml:"SURRENDERCHARGE"`
}

// FLATEXTRA ...
type FLATEXTRA struct {
	FLATEXTRATYPE      string `xml:"FLATEXTRA_TYPE"`
	FLATEXTRADURATION  int    `xml:"FLATEXTRA_DURATION"`
	FLATEXTRAAMOUNT    string `xml:"FLATEXTRA_AMOUNT"`
	FLATEXTRARATE      string `xml:"FLATEXTRA_RATE"`
	FLATEXTRASTARTDATE string `xml:"FLATEXTRA_START_DATE"`
}

// PARTICIPANT ...
type PARTICIPANT struct {
	PARTYID           string     `xml:"PARTYID"`
	SUBSTANDARDRATING string     `xml:"SUBSTANDARD_RATING"`
	FLATEXTRA         *FLATEXTRA `xml:"FLATEXTRA"`
}

// COVERAGEPARTICIPANTS ...
type COVERAGEPARTICIPANTS struct {
	XMLName     xml.Name       `xml:"COVERAGE_PARTICIPANTS"`
	PARTICIPANT []*PARTICIPANT `xml:"PARTICIPANT"`
}

// COVERAGE ...
type COVERAGE struct {
	COVERAGEID              string                `xml:"COVERAGE_ID"`
	COVERAGETYPE            string                `xml:"COVERAGE_TYPE"`
	COVERAGENAME            string                `xml:"COVERAGE_NAME"`
	CURRENTAMOUNT           string                `xml:"CURRENT_AMOUNT"`
	ORIGINALCOVERAGEAMOUNT  string                `xml:"ORIGINAL_COVERAGE_AMOUNT"`
	MINCOVERAGEAMOUNT       string                `xml:"MIN_COVERAGE_AMOUNT"`
	MAXCOVERAGEAMOUNT       string                `xml:"MAX_COVERAGE_AMOUNT"`
	GROSSDEATHBENEFITAMOUNT string                `xml:"GROSS_DEATHBENEFIT_AMOUNT"`
	LOWDEATHBENEFITAMOUNT   string                `xml:"LOW_DEATHBENEFIT_AMOUNT"`
	COVERAGECHANGEAMOUNT    string                `xml:"COVERAGE_CHANGE_AMOUNT"`
	COVERAGEEFFDATE         string                `xml:"COVERAGE_EFF_DATE"`
	COVERAGECHANGEEFFDATE   string                `xml:"COVERAGE_CHANGE_EFF_DATE"`
	COVERAGETERMINATIONDATE string                `xml:"COVERAGE_TERMINATION_DATE"`
	UNITOFCOVERAGE          string                `xml:"UNIT_OF_COVERAGE"`
	VALUEPERUNITOFCOVERAGE  string                `xml:"VALUE_PER_UNIT_OF_COVERAGE"`
	GUIDELINESINGLEPREMIUM  string                `xml:"GUIDELINE_SINGLE_PREMIUM"`
	GUIDELINELEVELPREMIUM   string                `xml:"GUIDELINE_LEVEL_PREMIUM"`
	SEVENPAYPREMIUM         string                `xml:"SEVEN_PAY_PREMIUM"`
	COVERAGEPARTICIPANTS    *COVERAGEPARTICIPANTS `xml:"COVERAGE_PARTICIPANTS"`
}

// COVERAGES ...
type COVERAGES struct {
	TOTALCOVERAGEAMOUNT                             string    `xml:"TOTAL_COVERAGE_AMOUNT"`
	CUMGROSSDEATHBENEFITAMOUNT                      string    `xml:"CUM_GROSS_DEATHBENEFIT_AMOUNT"`
	NETDEATHBENEFIT                                 string    `xml:"NET_DEATH_BENEFIT"`
	REMAININGDEATHBENEFITAMOUNT                     string    `xml:"REMAINING_DEATH_BENEFIT_AMOUNT"`
	MINCOVERAGEAMOUNT                               string    `xml:"MIN_COVERAGE_AMOUNT"`
	COVERAGEMAXANNUALCOVERAGECHANGEALLOWEDPERPOLICY string    `xml:"COVERAGE_MAX_ANNUAL_COVERAGE_CHANGE_ALLOWED_PER_POLICY"`
	COVERAGEDEATHBENEFITOPTIONSTARTDATE             string    `xml:"COVERAGE_DEATH_BENEFIT_OPTION_START_DATE"`
	COVERAGEDEATHBENEFITTESTOPTION                  string    `xml:"COVERAGE_DEATH_BENEFIT_TEST_OPTION"`
	COVERAGEAVALDEATHBENEFITOPTION                  string    `xml:"COVERAGE_AVAL_DEATH_BENEFIT_OPTION"`
	COVERAGERISKCLASSCHANGEALLOWEDYEAR              string    `xml:"COVERAGE_RISK_CLASS_CHANGE_ALLOWED_YEAR"`
	COVERAGECHARGERATEMONTHLY                       string    `xml:"COVERAGE_CHARGE_RATE_MONTHLY"`
	COVERAGEASSESTCHARGERATEMONTHLY                 string    `xml:"COVERAGE_ASSEST_CHARGE_RATE_MONTHLY"`
	MAXCOVERAGEAMOUNT                               string    `xml:"MAX_COVERAGE_AMOUNT"`
	COVERAGECHANGEEFFDATE                           string    `xml:"COVERAGE_CHANGE_EFF_DATE"`
	COVERAGEBAND                                    string    `xml:"COVERAGE_BAND"`
	MAXANNCOVCHANGEALLOWPERPOLICY                   string    `xml:"MAX_ANN_COV_CHANGE_ALLOW_PER_POLICY"`
	MINCOVERAGEDECREASEAMOUNT                       string    `xml:"MIN_COVERAGE_DECREASE_AMOUNT"`
	MAXCOVERAGEDECREASEAMOUNT                       uint8     `xml:"MAX_COVERAGE_DECREASE_AMOUNT"`
	MAXAGENOCOVERAGEAMTDECREASE                     string    `xml:"MAX_AGE_NO_COVERAGE_AMT_DECREASE"`
	COVERAGEAMTDECREASEALLOWED                      string    `xml:"COVERAGE_AMT_DECREASE_ALLOWED"`
	MAXAGENOCOVERAGEAMTINCREASE                     uint8     `xml:"MAX_AGE_NO_COVERAGE_AMT_INCREASE"`
	COVERAGEAMTINCREASEALLOWED                      uint8     `xml:"COVERAGE_AMT_INCREASE_ALLOWED"`
	MINCOVERAGEINCREASEAMOUNT                       string    `xml:"MIN_COVERAGE_INCREASE_AMOUNT"`
	MAXCOVERAGEINCREASEAMOUNT                       string    `xml:"MAX_COVERAGE_INCREASE_AMOUNT"`
	COVERAGERISKCLASSCHANGECOUNT                    string    `xml:"COVERAGE_RISK_CLASS_CHANGE_COUNT"`
	COVERAGE                                        *COVERAGE `xml:"COVERAGE"`
}

// POLICYCOVERAGES ...
type POLICYCOVERAGES struct {
	COVERAGES []*COVERAGES `xml:"COVERAGES"`
}

// POLICYLOAN ...
type POLICYLOAN struct {
	PLOANCONT               string  `xml:"PLOAN_CONT"`
	PLOANID                 string  `xml:"PLOAN_ID"`
	PLOANPREMMODEAMT        float64 `xml:"PLOAN_PREM_MODE_AMT"`
	PLOANDUEDATE            string  `xml:"PLOAN_DUE_DATE"`
	PLOANPREMDUEDATE        string  `xml:"PLOAN_PREM_DUE_DATE"`
	PLOANSURRAMT            float64 `xml:"PLOAN_SURR_AMT"`
	PLOANCASHSURRVALUE      float64 `xml:"PLOAN_CASH_SURR_VALUE"`
	PLOANBALANCE            float64 `xml:"PLOAN_BALANCE"`
	LOANTOTALBALANCE        float64 `xml:"LOAN_TOTAL_BALANCE"`
	LOANTOTALPRINCIPAL      float64 `xml:"LOAN_TOTAL_PRINCIPAL"`
	LOANPAYOFFAMT           float64 `xml:"LOAN_PAYOFF_AMT"`
	LOANMAXAMT              string  `xml:"LOAN_MAX_AMT"`
	LOANMINAMT              string  `xml:"LOAN_MIN_AMT"`
	LOANTOTALACCRUEDINT     string  `xml:"LOAN_TOTAL_ACCRUED_INT"`
	LOANMAXINTERESTRATE     string  `xml:"LOAN_MAX_INTEREST_RATE"`
	LOANCURRENTINTERESTRATE float64 `xml:"LOAN_CURRENT_INTEREST_RATE"`
	LOANLASTINTDUEDATE      string  `xml:"LOAN_LAST_INT_DUEDATE"`
	LOANTOTALNOOFLOAN       int     `xml:"LOAN_TOTAL_NO_OF_LOAN"`
	LOANINTERESTMETHOD      string  `xml:"LOAN_INTEREST_METHOD"`
	LOANTOTALYTDTAKEN       float64 `xml:"LOAN_TOTAL_YTD_TAKEN"`
	LOANREPAYMENTTYPE       string  `xml:"LOAN_REPAYMENT_TYPE"`
	LOANMINREPAYMENT        string  `xml:"LOAN_MIN_REPAYMENT"`
	LOANCHARGEINTERESTRATE  string  `xml:"LOAN_CHARGE_INTEREST_RATE"`
}

// POLICYLOANS ...
type POLICYLOANS struct {
	POLICYLOAN []*POLICYLOAN `xml:"POLICYLOAN"`
}

// WITHDRAWALVALUE ...
type WITHDRAWALVALUE struct {
	WITHDRAWALTOTALAMT                      string  `xml:"WITHDRAWAL_TOTAL_AMT"`
	WITHDRAWALFREEAMT                       string  `xml:"WITHDRAWAL_FREE_AMT"`
	WITHDRAWALMINAMT                        string  `xml:"WITHDRAWAL_MIN_AMT"`
	WITHDRAWALMAXAMT                        string  `xml:"WITHDRAWAL_MAX_AMT"`
	WITHDRAWALMAXPERCENTAGE                 string  `xml:"WITHDRAWAL_MAX_PERCENTAGE"`
	WITHDRAWALANNUALLIMITNOCOVDECREASE      float64 `xml:"WITHDRAWAL_ANNUAL_LIMIT_NO_COV_DECREASE"`
	WITHDRAWALANNUALPERCENTAGENOCOVDECREASE string  `xml:"WITHDRAWAL_ANNUAL_PERCENTAGE_NO_COV_DECREASE"`
	WITHDRAWALMAXREQUESTDURINGVESTINGPERIOD string  `xml:"WITHDRAWAL_MAX_REQUEST_DURING_VESTING_PERIOD"`
	WITHDRAWALMAXREQUESTAFTERVESTINGPERIOD  int     `xml:"WITHDRAWAL_MAX_REQUEST_AFTER_VESTING_PERIOD"`
	WITHDRAWALTAKENTOTALYTD                 string  `xml:"WITHDRAWAL_TAKEN_TOTAL_YTD"`
	WITHDRAWALNUMBEROFWITHDRAWAL            int     `xml:"WITHDRAWAL_NUMBER_OF_WITHDRAWAL"`
	WITHDRAWALALLOWEDSTARTDATE              string  `xml:"WITHDRAWAL_ALLOWED_START_DATE"`
	WITHDRAWALALLOWEDSTARTYEAR              int     `xml:"WITHDRAWAL_ALLOWED_START_YEAR"`
	WITHDRAWALMAXAGENOCOVERAGEAMTDECREASE   string  `xml:"WITHDRAWAL_MAX_AGE_NO_COVERAGE_AMT_DECREASE"`
}

// WITHDRAWALVALUES ...
type WITHDRAWALVALUES struct {
	WITHDRAWALVALUE []*WITHDRAWALVALUE `xml:"WITHDRAWALVALUE"`
}

// LOAN ...
type LOAN struct {
	LOANID                        string `xml:"LOAN_ID"`
	LOANTYPE                      string `xml:"LOAN_TYPE"`
	LOANINTERESTTYPE              string `xml:"LOAN_INTEREST_TYPE"`
	LOANINTERESTDUE               string `xml:"LOAN_INTEREST_DUE"`
	LOANPRINCIPAL                 string `xml:"LOAN_PRINCIPAL"`
	LOANBALANCE                   string `xml:"LOAN_BALANCE"`
	LOANCOLLATERALAMOUNT          string `xml:"LOAN_COLLATERAL_AMOUNT"`
	LOANINTERESTRATE              string `xml:"LOAN_INTEREST_RATE"`
	LOANCREDITRATE                string `xml:"LOAN_CREDIT_RATE"`
	LOANACCRUEDINTEREST           string `xml:"LOAN_ACCRUED_INTEREST"`
	LOANCOLLATERALACCRUEDINTEREST string `xml:"LOAN_COLLATERAL_ACCRUED_INTEREST"`
	LOANTOTALYTDTAKEN             string `xml:"LOAN_TOTAL_YTD_TAKEN"`
	LOANGLFUNDCODE                string `xml:"LOAN_GL_FUNDCODE"`
	LOANSTARTDATE                 string `xml:"LOAN_START_DATE"`
	LOANENDDATE                   string `xml:"LOAN_END_DATE"`
}

// LOANSEGMENTS ...
type LOANSEGMENTS struct {
	LOAN *LOAN `xml:"LOAN"`
}

// MATCH ...
type MATCH struct {
	MATCHID               string  `xml:"MATCH_ID"`
	MATCHACCVALUE         float64 `xml:"MATCH_ACC_VALUE"`
	MATCHCUMMPAYAMT       float64 `xml:"MATCH_CUMM_PAY_AMT"`
	MATCHYTDVALUE         string  `xml:"MATCH_YTD_VALUE"`
	MATCHMAXLIFEVESTAMT   string  `xml:"MATCH_MAX_LIFE_VEST_AMT"`
	MATCHPERCENTAGE       string  `xml:"MATCH_PERCENTAGE"`
	MATCHANNVESTAMT       float64 `xml:"MATCH_ANN_VEST_AMT"`
	MATCHMINPAYMENT       float64 `xml:"MATCH_MIN_PAYMENT"`
	MATCHPERIOD           string  `xml:"MATCH_PERIOD"`
	MATCHSTARTDATE        string  `xml:"MATCH_START_DATE"`
	MATCHENDDATE          string  `xml:"MATCH_END_DATE"`
	MATCHVESTINGPERIOD    int     `xml:"MATCH_VESTING_PERIOD"`
	MATCHVESTINGSTARTDATE string  `xml:"MATCH_VESTING_START_DATE"`
	MATCHVESTINGENDDATE   string  `xml:"MATCH_VESTING_END_DATE"`
	MATCHGLFUNDCODE       string  `xml:"MATCH_GL_FUND_CODE"`
}

// POLICYMATCH ...
type POLICYMATCH struct {
	MATCH *MATCH `xml:"MATCH"`
}

// POLICYPREMIUM ...
type POLICYPREMIUM struct {
	PREMIUMMODE        string  `xml:"PREMIUM_MODE"`
	PREMIUMAMT         string  `xml:"PREMIUM_AMT"`
	PREMIUMTYPE        string  `xml:"PREMIUM_TYPE"`
	PREMIUMSTARTDATE   string  `xml:"PREMIUM_START_DATE"`
	PREMIUMLASTPAYDATE string  `xml:"PREMIUM_LAST_PAY_DATE"`
	PREMIUMNEXTPAYDATE string  `xml:"PREMIUM_NEXT_PAY_DATE"`
	PREMIUMMINAMT      float64 `xml:"PREMIUM_MIN_AMT"`
	PREMIUMDUEDATE     string  `xml:"PREMIUM_DUE_DATE"`
}

// POLICYPREMIUMS ...
type POLICYPREMIUMS struct {
	POLICYPREMIUM []*POLICYPREMIUM `xml:"POLICYPREMIUM"`
}

// PAYMENTPARTYROLE ...
type PAYMENTPARTYROLE struct {
	PAYMENTPARTYROLE  string `xml:"PAYMENT_PARTY_ROLE"`
	PAYMENTPARTYID    string `xml:"PAYMENT_PARTY_ID"`
	PAYMENTBANKID     string `xml:"PAYMENT_BANK_ID"`
	PAYMENTPERCENTAGE string `xml:"PAYMENT_PERCENTAGE"`
}

// PAYMENTPARTYDETAILS ...
type PAYMENTPARTYDETAILS struct {
	XMLName          xml.Name          `xml:"PAYMENT_PARTY_DETAILS"`
	PAYMENTPARTYROLE *PAYMENTPARTYROLE `xml:"PAYMENTPARTYROLE"`
}

// POLICYPAYMENT ...
type POLICYPAYMENT struct {
	PAYMENTARRID              string               `xml:"PAYMENT_ARR_ID"`
	PAYMENTARRTYPE            string               `xml:"PAYMENT_ARR_TYPE"`
	REASON                    string               `xml:"REASON"`
	PAYMENTTYPE               string               `xml:"PAYMENT_TYPE"`
	PAYMENTSTATUS             string               `xml:"PAYMENT_STATUS"`
	PAYMENTFORM               string               `xml:"PAYMENT_FORM"`
	PAYMENTFREQUENCY          string               `xml:"PAYMENT_FREQUENCY"`
	PAYMENTNUMBEROFOCCURRENCE int                  `xml:"PAYMENT_NUMBER_OF_OCCURRENCE"`
	PAYMENTDISBURSEMENTTYPE   string               `xml:"PAYMENT_DISBURSEMENT_TYPE"`
	PAYMENTAMT                float64              `xml:"PAYMENT_AMT"`
	PAYMENTREQUESTDATE        string               `xml:"PAYMENT_REQUEST_DATE"`
	PAYMENTBEGINDATE          string               `xml:"PAYMENT_BEGIN_DATE"`
	PAYMENTENDDATE            string               `xml:"PAYMENT_END_DATE"`
	PAYMENTLASTACTIVITYDATE   string               `xml:"PAYMENT_LAST_ACTIVITY_DATE"`
	PAYMENTNEXTACTIVITYDATE   string               `xml:"PAYMENT_NEXT_ACTIVITY_DATE"`
	PAYMENTPARTYDETAILS       *PAYMENTPARTYDETAILS `xml:"PAYMENT_PARTY_DETAILS"`
}

// POLICYPAYMENTS ...
type POLICYPAYMENTS struct {
	POLICYPAYMENT []*POLICYPAYMENT `xml:"POLICYPAYMENT"`
}

// FUND ...
type FUND struct {
	FNDSEGID           string  `xml:"FND_SEG_ID"`
	FNDCONT            string  `xml:"FND_CONT"`
	FNDACCCODE         string  `xml:"FND_ACC_CODE"`
	FNDDIVCODE         string  `xml:"FND_DIV_CODE"`
	FNDFUNDMKTGNAME    string  `xml:"FND_FUND_MKTG_NAME"`
	FNDFUNDCORRNAME    string  `xml:"FND_FUND_CORR_NAME"`
	FNDACCTYPE         string  `xml:"FND_ACC_TYPE"`
	FNDACCSUBTYPE      string  `xml:"FND_ACC_SUB_TYPE"`
	FNDENDBAL          string  `xml:"FND_END_BAL"`
	FNDALLOCPERC       string  `xml:"FND_ALLOC_PERC"`
	FNDRENEWDATE       string  `xml:"FND_RENEW_DATE"`
	FNDFUNDSORTORDER   string  `xml:"FND_FUND_SORT_ORDER"`
	FNDCLOSINGIND      string  `xml:"FND_CLOSING_IND"`
	FNDMAXALLOCPERC    float64 `xml:"FND_MAX_ALLOC_PERC"`
	FNDINTRATE         float64 `xml:"FND_INT_RATE"`
	FNDGUARPRD         string  `xml:"FND_GUAR_PRD"`
	FNDDEPOSITDATE     string  `xml:"FND_DEPOSIT_DATE"`
	FNDINDCAPRATE      string  `xml:"FND_IND_CAP_RATE"`
	FNDGUARMINCAPRATE  string  `xml:"FND_GUAR_MIN_CAP_RATE"`
	FNDINDPARTRATE     string  `xml:"FND_IND_PART_RATE"`
	FNDGUARMINPARTRATE string  `xml:"FND_GUAR_MIN_PART_RATE"`
	FNDINDTRIGGER      string  `xml:"FND_IND_TRIGGER"`
	FNDGUARMINTRIGGER  string  `xml:"FND_GUAR_MIN_TRIGGER"`
	FNDINDEXNAME       string  `xml:"FND_INDEX_NAME"`
	FNDGUARMININTRATE  string  `xml:"FND_GUAR_MIN_INT_RATE"`
}

// FUNDS ...
type FUNDS struct {
	FUND []*FUND `xml:"FUND"`
}

// WITHDRAWALPARTY ...
type WITHDRAWALPARTY struct {
	XMLName              xml.Name `xml:"WITHDRAWAL_PARTY"`
	WITHDRAWALPARTYROLE  string   `xml:"WITHDRAWAL_PARTY_ROLE"`
	WITHDRAWALPARTYID    string   `xml:"WITHDRAWAL_PARTY_ID"`
	WITHDRAWALBANKID     string   `xml:"WITHDRAWAL_BANK_ID"`
	WITHDRAWALPERCENTAGE uint8    `xml:"WITHDRAWAL_PERCENTAGE"`
}

// WITHDRAWALPARTYDETAILS ...
type WITHDRAWALPARTYDETAILS struct {
	XMLName         xml.Name         `xml:"WITHDRAWAL_PARTY_DETAILS"`
	WITHDRAWALPARTY *WITHDRAWALPARTY `xml:"WITHDRAWAL_PARTY"`
}

// POLICYWITHDRAWAL ...
type POLICYWITHDRAWAL struct {
	WITHDRAWALARRTYPE            *WITHDRAWALARRTYPE      `xml:"WITHDRAWAL_ARR_TYPE"`
	WITHDRAWALARRID              string                  `xml:"WITHDRAWAL_ARR_ID"`
	WITHDRAWALSTATUS             string                  `xml:"WITHDRAWAL_STATUS"`
	WITHDRAWALFORM               string                  `xml:"WITHDRAWAL_FORM"`
	WITHDRAWALFREQUENCY          string                  `xml:"WITHDRAWAL_FREQUENCY"`
	WITHDRAWALNUMBEROFOCCURRENCE int                     `xml:"WITHDRAWAL_NUMBER_OF_OCCURRENCE"`
	WITHDRAWALDISBURSEMENTTYPE   string                  `xml:"WITHDRAWAL_DISBURSEMENT_TYPE"`
	WITHDRAWALAMT                uint8                   `xml:"WITHDRAWAL_AMT"`
	WITHDRAWALREQUESTDATE        string                  `xml:"WITHDRAWAL_REQUEST_DATE"`
	WITHDRAWALBEGINDATE          string                  `xml:"WITHDRAWAL_BEGIN_DATE"`
	WITHDRAWALENDDATE            string                  `xml:"WITHDRAWAL_END_DATE"`
	WITHDRAWALTLASTACTIVITYDATE  string                  `xml:"WITHDRAWALT_LAST_ACTIVITY_DATE"`
	WITHDRAWALNEXTACTIVITYDATE   string                  `xml:"WITHDRAWAL_NEXT_ACTIVITY_DATE"`
	WITHDRAWALPARTYDETAILS       *WITHDRAWALPARTYDETAILS `xml:"WITHDRAWAL_PARTY_DETAILS"`
}

// POLICYWITHDRAWALS ...
type POLICYWITHDRAWALS struct {
	POLICYWITHDRAWAL *POLICYWITHDRAWAL `xml:"POLICYWITHDRAWAL"`
}

// POLICYBANK ...
type POLICYBANK struct {
	BANKID                 string `xml:"BANK_ID"`
	BANKPARTYID            string `xml:"BANK_PARTY_ID"`
	BANKSTARTDATE          string `xml:"BANK_START_DATE"`
	BANKENDDATE            string `xml:"BANK_END_DATE"`
	BANKSTATUS             string `xml:"BANK_STATUS"`
	BANKACCTOKEN           string `xml:"BANK_ACC_TOKEN"`
	BANKHOLDNAME           string `xml:"BANK_HOLD_NAME"`
	BANKNAME               string `xml:"BANK_NAME"`
	BANKACCTYPE            string `xml:"BANK_ACC_TYPE"`
	BANKROUTNUM            string `xml:"BANK_ROUT_NUM"`
	BANKACCNUM             string `xml:"BANK_ACC_NUM"`
	BANKCREDITCARDNUM      string `xml:"BANK_CREDIT_CARD_NUM"`
	BANKCREDITEXPDATE      string `xml:"BANK_CREDIT_EXP_DATE"`
	BANKCREDITCARDTYPE     string `xml:"BANK_CREDIT_CARD_TYPE"`
	BANKCREDITDEBITTYPE    string `xml:"BANK_CREDIT_DEBIT_TYPE"`
	BANKINSTITUTIONNUM     string `xml:"BANK_INSTITUTION_NUM"`
	BANKACCPURPOSE         string `xml:"BANK_ACC_PURPOSE"`
	BANKLASTUPDATEDATETIME string `xml:"BANK_LAST_UPDATE_DATETIME"`
}

// POLICYBANKINFO ...
type POLICYBANKINFO struct {
	POLICYBANK []*POLICYBANK `xml:"POLICYBANK"`
}

// FUNDSEGMENT ...
type FUNDSEGMENT struct {
	XMLName                          xml.Name `xml:"FUND_SEGMENT"`
	FUNDSEGMENTID                    uint8    `xml:"FUND_SEGMENT_ID"`
	FUNDID                           string   `xml:"FUND_ID"`
	FUNDSEGMENTORIGINALDEPOSITAMOUNT float64  `xml:"FUND_SEGMENT_ORIGINAL_DEPOSIT_AMOUNT"`
	FUNDSEGMENTORIGINALDEPOSITDATE   string   `xml:"FUND_SEGMENT_ORIGINAL_DEPOSIT_DATE"`
	FUNDSEGMENTDEPOSITDATE           string   `xml:"FUND_SEGMENT_DEPOSIT_DATE"`
	FUNDSEGMENTDEPOSITAMOUNT         float64  `xml:"FUND_SEGMENT_DEPOSIT_AMOUNT"`
	FUNDSEGMENTCURRENTAMOUNT         float64  `xml:"FUND_SEGMENT_CURRENT_AMOUNT"`
	FUNDSEGMENTRENEWALDATE           string   `xml:"FUND_SEGMENT_RENEWAL_DATE"`
	FUNDSEGMENTNUMBEROFUNITS         string   `xml:"FUND_SEGMENT_NUMBER_OF_UNITS"`
	FUNDSEGMENTSWEEPACCOUNTID        string   `xml:"FUND_SEGMENT_SWEEP_ACCOUNT_ID"`
	FUNDSEGMENTSTARTDATE             uint8    `xml:"FUND_SEGMENT_START_DATE"`
	FUNDSEGMENTENDDATE               string   `xml:"FUND_SEGMENT_END_DATE"`
}

// FUNDSEGMENTS ...
type FUNDSEGMENTS struct {
	XMLName     xml.Name     `xml:"FUND_SEGMENTS"`
	FUNDSEGMENT *FUNDSEGMENT `xml:"FUND_SEGMENT"`
}

// POLICYFUND ...
type POLICYFUND struct {
	FUNDID                       string        `xml:"FUND_ID"`
	FUNDPRODUCTCODE              string        `xml:"FUND_PRODUCT_CODE"`
	FUNDNAME                     string        `xml:"FUND_NAME"`
	FUNDACCOUNTTYPE              string        `xml:"FUND_ACCOUNT_TYPE"`
	FUNDINDEXNAME                string        `xml:"FUND_INDEX_NAME"`
	FUNDALLOCATIONPERCENTAGE     uint8         `xml:"FUND_ALLOCATION_PERCENTAGE"`
	FUNDGLFUNDCODE               uint8         `xml:"FUND_GL_FUND_CODE"`
	FUNDTOTALFUNDVALUE           float64       `xml:"FUND_TOTAL_FUND_VALUE"`
	FUNDSTARTDATE                string        `xml:"FUND_START_DATE"`
	FUNDENDDATE                  string        `xml:"FUND_END_DATE"`
	FUNDSEGMENTS                 *FUNDSEGMENTS `xml:"FUND_SEGMENTS"`
	FUNDMINHOLDINGACCTRANSFERAMT string        `xml:"FUND_MIN_HOLDING_ACC_TRANSFER_AMT"`
	FUNDMINFUNDTRANSFERAMT       float64       `xml:"FUND_MIN_FUND_TRANSFER_AMT"`
	FUNDMINREQUIREDACCOUNTVALUE  float64       `xml:"FUND_MIN_REQUIRED_ACCOUNT_VALUE"`
}

// POLICYFUNDS ...
type POLICYFUNDS struct {
	POLICYFUND []*POLICYFUND `xml:"POLICYFUND"`
}

// FEATUREFEE ...
type FEATUREFEE struct {
	XMLName   xml.Name `xml:"FEATURE_FEE"`
	FEAFEEAMT float64  `xml:"FEA_FEE_AMT"`
	FEAFEEPCT float64  `xml:"FEA_FEE_PCT"`
}

// FEATURE ...
type FEATURE struct {
	FEATURECODE        string      `xml:"FEATURE_CODE"`
	FEATUREEXTCODE     string      `xml:"FEATURE_EXT_CODE"`
	FEATURETYPE        string      `xml:"FEATURE_TYPE"`
	FEATURENAME        string      `xml:"FEATURE_NAME"`
	FEATUREGROUPID     string      `xml:"FEATURE_GROUP_ID"`
	FEATURESTATUS      bool        `xml:"FEATURE_STATUS"`
	FEATURESTARTDATE   string      `xml:"FEATURE_START_DATE"`
	FEATUREENDDATE     string      `xml:"FEATURE_END_DATE"`
	FEATUREEFFDATE     string      `xml:"FEATURE_EFF_DATE"`
	FEATUREAPPROVEDATE string      `xml:"FEATURE_APPROVE_DATE"`
	FEATURETOTREQAMT   float64     `xml:"FEATURE_TOT_REQ_AMT"`
	FEATURETOTMINAMT   float64     `xml:"FEATURE_TOT_MIN_AMT"`
	FEATUREPAYAMT      float64     `xml:"FEATURE_PAY_AMT"`
	FEATURETOTPAYAMT   float64     `xml:"FEATURE_TOT_PAY_AMT"`
	FEATUREFEE         *FEATUREFEE `xml:"FEATURE_FEE"`
}

// FEATURES ...
type FEATURES struct {
	FEATURE []*FEATURE `xml:"FEATURE"`
}

// RIDERCHARGE ...
type RIDERCHARGE struct {
	XMLName                 xml.Name `xml:"RIDER_CHARGE"`
	RIDEREXERCISECHARGE     string   `xml:"RIDER_EXERCISE_CHARGE"`
	RIDEREXERCISECHARGERATE string   `xml:"RIDER_EXERCISE_CHARGE_RATE"`
}

// POLICYRIDER ...
type POLICYRIDER struct {
	RIDERTYPE                                      string       `xml:"RIDER_TYPE"`
	RIDERCODE                                      string       `xml:"RIDER_CODE"`
	RIDERNAME                                      string       `xml:"RIDER_NAME"`
	RIDERELECTED                                   string       `xml:"RIDER_ELECTED"`
	RIDEREFFECTIVEDATE                             string       `xml:"RIDER_EFFECTIVE_DATE"`
	RIDERSTATUS                                    string       `xml:"RIDER_STATUS"`
	RIDEREXERCISEDATE                              string       `xml:"RIDER_EXERCISE_DATE"`
	RIDERTERMINATIONDATE                           string       `xml:"RIDER_TERMINATION_DATE"`
	RIDERCOVERAGEID                                string       `xml:"RIDER_COVERAGE_ID"`
	RIDERAMOUNT                                    string       `xml:"RIDER_AMOUNT"`
	RIDERCOVERAGEAMT                               string       `xml:"RIDER_COVERAGE_AMT"`
	RIDERCHARGE                                    *RIDERCHARGE `xml:"RIDER_CHARGE"`
	RIDERMINSAFEGAURDAGE                           string       `xml:"RIDER_MIN_SAFEGAURD_AGE"`
	RIDERMINSAFEGAURDLOANPAYOFFAMTPER              string       `xml:"RIDER_MIN_SAFEGAURD_LOAN_PAYOFFAMT_PER"`
	RIDERMINSAFEGAURDPOLICYYEAR                    string       `xml:"RIDER_MIN_SAFEGAURD_POLICY_YEAR"`
	RIDERTERMINALHEALTHEVENT                       string       `xml:"RIDER_TERMINAL_HEALTH_EVENT"`
	RIDERMAXACCELEBENEFITPERCENTAGE                string       `xml:"RIDER_MAX_ACCELE_BENEFIT_PERCENTAGE"`
	RIDERMAXBENEFITAMT                             float64      `xml:"RIDER_MAX_BENEFIT_AMT"`
	RIDERMAXCLAIMCOUNT                             string       `xml:"RIDER_MAX_CLAIM_COUNT"`
	RIDERMAXADMINCHARGE                            string       `xml:"RIDER_MAX_ADMIN_CHARGE"`
	RIDERMINACCELEAMOUNT                           string       `xml:"RIDER_MIN_ACCELE_AMOUNT"`
	RIDERMAXACCELEAMOUNT                           string       `xml:"RIDER_MAX_ACCELE_AMOUNT"`
	RIDERTIERIMAXCRITICALILLNESSBENEFITPERCENTAGE  string       `xml:"RIDER_TIER_I_MAX_CRITICAL_ILLNESS_BENEFIT_PERCENTAGE"`
	RIDERTIERIMAXCRITICALILLNESSBENEFITAMOUNT      string       `xml:"RIDER_TIER_I_MAX_CRITICAL_ILLNESS_BENEFIT_AMOUNT"`
	RIDERTIERIIMAXCRITICALILLNESSBENEFITPERCENTAGE string       `xml:"RIDER_TIER_II_MAX_CRITICAL_ILLNESS_BENEFIT_PERCENTAGE"`
	RIDERTIERIIMAXCRITICALILLNESSBENEFITAMOUNT     string       `xml:"RIDER_TIER_II_MAX_CRITICAL_ILLNESS_BENEFIT_AMOUNT"`
	RIDERSIBBONUSINTPER                            string       `xml:"RIDER_SIB_BONUS_INT_PER"`
	RIDERSIBMAXBONUSAMOUNT                         string       `xml:"RIDER_SIB_MAX_BONUS_AMOUNT"`
}

// POLICYRIDERS ...
type POLICYRIDERS struct {
	POLICYRIDER []*POLICYRIDER `xml:"POLICYRIDER"`
}

// DEATHBENEFIT ...
type DEATHBENEFIT struct {
	XMLName                         xml.Name `xml:"DEATH_BENEFIT"`
	DEATHBENEFITOPTION              string   `xml:"DEATH_BENEFIT_OPTION"`
	DEATHBENEFITOPTIONEFFECTIVEDATE string   `xml:"DEATH_BENEFIT_OPTION_EFFECTIVE_DATE"`
}

// GUIDELINEPREMIUM ...
type GUIDELINEPREMIUM struct {
	XMLName                              xml.Name `xml:"GUIDELINE_PREMIUM"`
	GUIDELINEPREMIUMTESTDATE             string   `xml:"GUIDELINE_PREMIUM_TEST_DATE"`
	DEFINITIONOFLIFEINSURANCE            string   `xml:"DEFINITION_OF_LIFE_INSURANCE"`
	GUIDELINESINGLEPREMIUM               float64  `xml:"GUIDELINE_SINGLE_PREMIUM"`
	GUIDELINELEVELPREMIUM                float64  `xml:"GUIDELINE_LEVEL_PREMIUM"`
	AMOUNTEXCESSTOGUIDELINE              uint8    `xml:"AMOUNT_EXCESS_TO_GUIDELINE"`
	TOTALGUIDELINELEVELPREMIUMSINCEISSUE float64  `xml:"TOTAL_GUIDELINE_LEVEL_PREMIUM_SINCE_ISSUE"`
}

// MODIFIEDENDOWMENTCONTRACT ...
type MODIFIEDENDOWMENTCONTRACT struct {
	XMLName           xml.Name `xml:"MODIFIED_ENDOWMENT_CONTRACT"`
	MECTESTDATE       string   `xml:"MEC_TEST_DATE"`
	AMOUNTEXCESSTOMEC uint8    `xml:"AMOUNT_EXCESS_TO_MEC"`
	MECSTATUSDATE     string   `xml:"MEC_STATUS_DATE"`
	MECSTATUS         string   `xml:"MEC_STATUS"`
	SEVENPAYTESTBASIS float64  `xml:"SEVEN_PAY_TEST_BASIS"`
	SEVENPAYPREMIUM   float64  `xml:"SEVEN_PAY_PREMIUM"`
	SEVENPAYSTARTDATE string   `xml:"SEVEN_PAY_START_DATE"`
	SEVENPAYPERIOD    string   `xml:"SEVEN_PAY_PERIOD"`
	SEVENPAYLIMIT     float64  `xml:"SEVEN_PAY_LIMIT"`
	YEARINPERIOD      uint8    `xml:"YEAR_IN_PERIOD"`
}

// TESTVALUES ...
type TESTVALUES struct {
	XMLName                   xml.Name                   `xml:"TEST_VALUES"`
	GUIDELINEPREMIUM          *GUIDELINEPREMIUM          `xml:"GUIDELINE_PREMIUM"`
	MODIFIEDENDOWMENTCONTRACT *MODIFIEDENDOWMENTCONTRACT `xml:"MODIFIED_ENDOWMENT_CONTRACT"`
}

// ILLUSTRATION ...
type ILLUSTRATION struct {
	ILLYEAR                          string `xml:"ILL_YEAR"`
	ILLPOLICYYEAR                    int    `xml:"ILL_POLICY_YEAR"`
	ILLINSUREDAGE                    int    `xml:"ILL_INSURED_AGE"`
	ILLOWNERAGE                      int    `xml:"ILL_OWNER_AGE"`
	ILLPREMIUM                       string `xml:"ILL_PREMIUM"`
	ILLANNUALPREMIUM                 string `xml:"ILL_ANNUAL_PREMIUM"`
	ILLMINGUARCONTVAL                string `xml:"ILL_MIN_GUAR_CONT_VAL"`
	ILLCURRCONTVAL                   string `xml:"ILL_CURR_CONT_VAL"`
	ILLSURRCHARGE                    string `xml:"ILL_SURR_CHARGE"`
	ILLGUARCONTWITHDRAWALVAL         string `xml:"ILL_GUAR_CONT_WITHDRAWAL_VAL"`
	ILLCURRCONTWITHDRAWALVAL         string `xml:"ILL_CURR_CONT_WITHDRAWAL_VAL"`
	ILLGUARIRR                       string `xml:"ILL_GUAR_IRR"`
	ILLCURRIRR                       string `xml:"ILL_CURR_IRR"`
	ILLGUARANTEEMAXEXPENSECHARGERATE string `xml:"ILL_GUARANTEE_MAX_EXPENSE_CHARGE_RATE"`
	ILLGUARANTEEUNITRATE             string `xml:"ILL_GUARANTEE_UNIT_RATE"`
	ILLGUARANTEEMINUNITCHARGE        string `xml:"ILL_GUARANTEE_MIN_UNIT_CHARGE"`
	ILLGUARANTEEMAXUNITCHARGE        string `xml:"ILL_GUARANTEE_MAX_UNIT_CHARGE"`
	ILLNHRATEPER                     string `xml:"ILL_NH_RATE_PER"`
	ILLNHANNGUARANTEED               string `xml:"ILL_NH_ANN_GUARANTEED"`
	ILLNHANNILLUSTRATED              string `xml:"ILL_NH_ANN_ILLUSTRATED"`
	ILLFIXMINCONTWITHDRAWALVAL       string `xml:"ILL_FIX_MIN_CONT_WITHDRAWAL_VAL"`
	ILLGVMINCONTVAL                  string `xml:"ILL_GV_MIN_CONT_VAL"`
	ILLGVMINCONTWITHDRAWALVAL        string `xml:"ILL_GV_MIN_CONT_WITHDRAWAL_VAL"`
	ILLDEFAULTVAL                    string `xml:"ILL_DEFAULT_VAL"`
	ILLDEATHBENEFIT                  string `xml:"ILL_DEATH_BENEFIT"`
	ILLCASHSURRENDERVALUE            string `xml:"ILL_CASHSURRENDER_VALUE"`
	ILLLIFESURRENDERCOSTINDEX        string `xml:"ILL_LIFE_SURRENDER_COST_INDEX"`
	ILLLIFENETPAYMENTCOSTINDEX       string `xml:"ILL_LIFE_NET_PAYMENT_COST_INDEX"`
	ILLSAFEGAURDRIDERCHARGES         string `xml:"ILL_SAFEGAURD_RIDER_CHARGES"`
	ILLSIBMINSAVINGPAYMENTS          string `xml:"ILL_SIB_MIN_SAVING_PAYMENTS"`
	ILLGUARMAXCOIRATEPER             string `xml:"ILL_GUAR_MAX_COI_RATE_PER"`
	ILLGUARMAXCOIRATE                string `xml:"ILL_GUAR_MAX_COI_RATE"`
	ILLCORRRATE                      string `xml:"ILL_CORR_RATE"`
}

// ILLUSTRATIONVALUES ...
type ILLUSTRATIONVALUES struct {
	ILLMAXCHARGE         uint8           `xml:"ILL_MAX_CHARGE"`
	ILLCOVERAGESTARTYEAR string          `xml:"ILL_COVERAGE_START_YEAR"`
	ILLUSTRATION         []*ILLUSTRATION `xml:"ILLUSTRATION"`
}

// TRANAMOUNTS ...
type TRANAMOUNTS struct {
	XMLName                     xml.Name `xml:"TRAN_AMOUNTS"`
	PAYMENTAMOUNT               float64  `xml:"PAYMENT_AMOUNT"`
	REQUESTEDAMOUNT             float64  `xml:"REQUESTED_AMOUNT"`
	APPLIEDAMOUNT               float64  `xml:"APPLIED_AMOUNT"`
	CALCULATEDAMOUNT            float64  `xml:"CALCULATED_AMOUNT"`
	CHARGEAMOUNT                float64  `xml:"CHARGE_AMOUNT"`
	TAXABLEAMOUNT               float64  `xml:"TAXABLE_AMOUNT"`
	AMOUNTTYPE                  string   `xml:"AMOUNT_TYPE"`
	REQUESTEDFUNDTRANSFERAMOUNT string   `xml:"REQUESTED_FUNDTRANSFER_AMOUNT"`
}

// FUNDDISTRIBUTIONSEGMENTS ...
type FUNDDISTRIBUTIONSEGMENTS struct {
	XMLName         xml.Name `xml:"FUND_DISTRIBUTION_SEGMENTS"`
	SEGMENTID       string   `xml:"SEGMENT_ID"`
	CURRENTAMOUNT   uint8    `xml:"CURRENT_AMOUNT"`
	REQUESTEDAMOUNT uint8    `xml:"REQUESTED_AMOUNT"`
}

// DISTRIBUTION ...
type DISTRIBUTION struct {
	FUNDID                   string                    `xml:"FUND_ID"`
	FUNDNAME                 string                    `xml:"FUND_NAME"`
	TOTALFUNDVALUE           uint8                     `xml:"TOTAL_FUND_VALUE"`
	REQUESTEDAMOUNT          uint8                     `xml:"REQUESTED_AMOUNT"`
	FUNDDISTRIBUTIONSEGMENTS *FUNDDISTRIBUTIONSEGMENTS `xml:"FUND_DISTRIBUTION_SEGMENTS"`
}

// FUNDDISTRIBUTIONS ...
type FUNDDISTRIBUTIONS struct {
	XMLName      xml.Name      `xml:"FUND_DISTRIBUTIONS"`
	DISTRIBUTION *DISTRIBUTION `xml:"DISTRIBUTION"`
}

// SEGMENTACTIVITY ...
type SEGMENTACTIVITY struct {
	XMLName       xml.Name `xml:"SEGMENT_ACTIVITY"`
	SEGMENTID     string   `xml:"SEGMENT_ID"`
	APPLIEDRATE   uint8    `xml:"APPLIED_RATE"`
	APPLIEDAMOUNT uint8    `xml:"APPLIED_AMOUNT"`
}

// FUNDACTIVITYSEGMENTS ...
type FUNDACTIVITYSEGMENTS struct {
	XMLName         xml.Name         `xml:"FUND_ACTIVITY_SEGMENTS"`
	SEGMENTID       string           `xml:"SEGMENT_ID"`
	CURRENTAMOUNT   uint8            `xml:"CURRENT_AMOUNT"`
	SEGMENTACTIVITY *SEGMENTACTIVITY `xml:"SEGMENT_ACTIVITY"`
}

// ACTIVITY ...
type ACTIVITY struct {
	FUNDID               string                `xml:"FUND_ID"`
	TOTALFUNDVALUE       uint8                 `xml:"TOTAL_FUND_VALUE"`
	FUNDACTIVITYSEGMENTS *FUNDACTIVITYSEGMENTS `xml:"FUND_ACTIVITY_SEGMENTS"`
}

// FUNDACTIVITIES ...
type FUNDACTIVITIES struct {
	XMLName  xml.Name  `xml:"FUND_ACTIVITIES"`
	ACTIVITY *ACTIVITY `xml:"ACTIVITY"`
}

// LOANSEGMENTS2 ...
type LOANSEGMENTS2 struct {
	XMLName             xml.Name         `xml:"LOAN_SEGMENTS"`
	SEGMENTID           string           `xml:"SEGMENT_ID"`
	LOANBALANCE         string           `xml:"LOAN_BALANCE"`
	LOANACCRUEDINTEREST string           `xml:"LOAN_ACCRUED_INTEREST"`
	SEGMENTACTIVITY     *SEGMENTACTIVITY `xml:"SEGMENT_ACTIVITY"`
}

// LOANACTIVTY ...
type LOANACTIVTY struct {
	XMLName                  xml.Name      `xml:"LOAN_ACTIVTY"`
	TOTALLOANBALANCE         string        `xml:"TOTAL_LOAN_BALANCE"`
	TOTALLOANACCRUEDINTEREST string        `xml:"TOTAL_LOAN_ACCRUED_INTEREST"`
	LOANSEGMENTS             *LOANSEGMENTS `xml:"LOAN_SEGMENTS"`
}

// LOANACTIVITIES ...
type LOANACTIVITIES struct {
	XMLName     xml.Name     `xml:"LOAN_ACTIVITIES"`
	LOANACTIVTY *LOANACTIVTY `xml:"LOAN_ACTIVTY"`
}

// TRANPARTY ...
type TRANPARTY struct {
	XMLName            xml.Name `xml:"TRAN_PARTY"`
	PARTYROLE          string   `xml:"PARTY_ROLE"`
	PARTYID            string   `xml:"PARTY_ID"`
	PERCENTAGE         uint8    `xml:"PERCENTAGE"`
	BANKID             string   `xml:"BANK_ID"`
	PAYMENTFORM        string   `xml:"PAYMENT_FORM"`
	DISBURSEMENTAMOUNT string   `xml:"DISBURSEMENT_AMOUNT"`
	GROSSAMOUNT        string   `xml:"GROSS_AMOUNT"`
}

// TAXWITHHOLDINGINSTRUCTIONS ...
type TAXWITHHOLDINGINSTRUCTIONS struct {
	XMLName            xml.Name `xml:"TAX_WITHHOLDING_INSTRUCTIONS"`
	PARTYROLE          string   `xml:"PARTY_ROLE"`
	PARTYID            string   `xml:"PARTY_ID"`
	TAXWITHHOLDINGTYPE string   `xml:"TAX_WITHHOLDING_TYPE"`
	TAXRATETOUSE       string   `xml:"TAX_RATE_TO_USE"`
	FILINGSTATUS       string   `xml:"FILING_STATUS"`
	DOLLAR             uint8    `xml:"DOLLAR"`
	PERCENTAGE         uint8    `xml:"PERCENTAGE"`
	EXEMPTIONS         uint8    `xml:"EXEMPTIONS"`
	TAXJURISDICTION    string   `xml:"TAX_JURISDICTION"`
}

// TAXWITHHELDAMOUNTS ...
type TAXWITHHELDAMOUNTS struct {
	XMLName               xml.Name `xml:"TAX_WITHHELD_AMOUNTS"`
	PARTYROLE             string   `xml:"PARTY_ROLE"`
	PARTYID               string   `xml:"PARTY_ID"`
	TAXWITHHOLDINGTYPE    string   `xml:"TAX_WITHHOLDING_TYPE"`
	WITHHELDAMOUNT        uint8    `xml:"WITHHELD_AMOUNT"`
	WITHHELDTAXABLEAMOUNT uint8    `xml:"WITHHELD_TAXABLE_AMOUNT"`
	APPLIEDTAXRATE        uint8    `xml:"APPLIED_TAX_RATE"`
}

// POLICYTRANSACTION ...
type POLICYTRANSACTION struct {
	TRANID                     string                      `xml:"TRAN_ID"`
	TRANTYPE                   string                      `xml:"TRAN_TYPE"`
	TRANARRID                  string                      `xml:"TRAN_ARR_ID"`
	TRANREQUESTDATE            string                      `xml:"TRAN_REQUEST_DATE"`
	TRANEFFECTIVEDATE          string                      `xml:"TRAN_EFFECTIVE_DATE"`
	TRANPROCESSDATE            string                      `xml:"TRAN_PROCESS_DATE"`
	TRANSTATUS                 string                      `xml:"TRAN_STATUS"`
	TRANACTION                 string                      `xml:"TRAN_ACTION"`
	TRANREASON                 string                      `xml:"TRAN_REASON"`
	TRANAMOUNTS                *TRANAMOUNTS                `xml:"TRAN_AMOUNTS"`
	PAYORROLE                  string                      `xml:"PAYOR_ROLE"`
	PAYORPARTYID               string                      `xml:"PAYOR_PARTY_ID"`
	PAYORPAYMENTFORM           string                      `xml:"PAYOR_PAYMENT_FORM"`
	PAYORBANKID                string                      `xml:"PAYOR_BANK_ID"`
	PAYEEROLE                  string                      `xml:"PAYEE_ROLE"`
	PAYEEPARTYID               string                      `xml:"PAYEE_PARTY_ID"`
	PAYEEPAYMENTFORM           string                      `xml:"PAYEE_PAYMENT_FORM"`
	PAYEEBANKID                string                      `xml:"PAYEE_BANK_ID"`
	FUNDDISTRIBUTIONS          *FUNDDISTRIBUTIONS          `xml:"FUND_DISTRIBUTIONS"`
	FUNDACTIVITIES             *FUNDACTIVITIES             `xml:"FUND_ACTIVITIES"`
	LOANACTIVITIES             *LOANACTIVITIES             `xml:"LOAN_ACTIVITIES"`
	TRANPARTY                  *TRANPARTY                  `xml:"TRAN_PARTY"`
	TAXWITHHOLDINGINSTRUCTIONS *TAXWITHHOLDINGINSTRUCTIONS `xml:"TAX_WITHHOLDING_INSTRUCTIONS"`
	TAXWITHHELDAMOUNTS         *TAXWITHHELDAMOUNTS         `xml:"TAX_WITHHELD_AMOUNTS"`
}

// POLICYTRANSACTIONS ...
type POLICYTRANSACTIONS struct {
	POLICYTRANSACTION []*POLICYTRANSACTION `xml:"POLICYTRANSACTION"`
}

// TRANSACTIONDETAIL ...
type TRANSACTIONDETAIL struct {
	TXDCONT         string  `xml:"TXD_CONT"`
	TXDACCCODE      string  `xml:"TXD_ACC_CODE"`
	TXDDIVCODE      string  `xml:"TXD_DIV_CODE"`
	TXDTRANSNUM     string  `xml:"TXD_TRANS_NUM"`
	TXDTRANSTYPE    string  `xml:"TXD_TRANS_TYPE"`
	TXDTRANSDESC    string  `xml:"TXD_TRANS_DESC"`
	TXDFUNDDIVNAME  string  `xml:"TXD_FUND_DIV_NAME"`
	TXDFUNDMKTGNAME string  `xml:"TXD_FUND_MKTG_NAME"`
	TXDTRANSAMT     float64 `xml:"TXD_TRANS_AMT"`
	TXDTRANSDATE    string  `xml:"TXD_TRANS_DATE"`
	TXDUNITVALUE    float64 `xml:"TXD_UNIT_VALUE"`
	TXDTXNUNITS     string  `xml:"TXD_TXN_UNITS"`
	TXDBEGUNITS     string  `xml:"TXD_BEG_UNITS"`
	TXDENDUNITS     string  `xml:"TXD_END_UNITS"`
	TXNFUNDNAME     string  `xml:"TXN_FUND_NAME"`
}

// TRANSACTIONDETAILS ...
type TRANSACTIONDETAILS struct {
	TRANSACTIONDETAIL []*TRANSACTIONDETAIL `xml:"TRANSACTIONDETAIL"`
}

// TRANSACTIONSUMMARY ...
type TRANSACTIONSUMMARY struct {
	TXNCONT            string              `xml:"TXN_CONT"`
	TXNTRANSNUM        string              `xml:"TXN_TRANS_NUM"`
	TXNTRANSDATE       string              `xml:"TXN_TRANS_DATE"`
	TXNTRANSTYPE       string              `xml:"TXN_TRANS_TYPE"`
	TXNTRANSDESC       string              `xml:"TXN_TRANS_DESC"`
	TXNTRANSSTATUS     string              `xml:"TXN_TRANS_STATUS"`
	TXNTOTALAMT        float64             `xml:"TXN_TOTAL_AMT"`
	TXNFEDTAXWITHD     float64             `xml:"TXN_FED_TAX_WITHD"`
	TXNSTATETAXWITHD   float64             `xml:"TXN_STATE_TAX_WITHD"`
	TXNBKUPTAXWITHD    float64             `xml:"TXN_BKUP_TAX_WITHD"`
	TXNNRATAXWITHD     float64             `xml:"TXN_NRA_TAX_WITHD"`
	TRANSACTIONDETAILS *TRANSACTIONDETAILS `xml:"TRANSACTIONDETAILS"`
}

// TRANSACTIONSUMMARIES ...
type TRANSACTIONSUMMARIES struct {
	TRANSACTIONSUMMARY []*TRANSACTIONSUMMARY `xml:"TRANSACTIONSUMMARY"`
}

// VENDOR ...
type VENDOR struct {
	VENDORBUSINESSNAME string `xml:"VENDOR_BUSINESS_NAME"`
	VENDORADDRLINE1    string `xml:"VENDOR_ADDR_LINE1"`
	VENDORADDRLINE2    string `xml:"VENDOR_ADDR_LINE2"`
	VENDORADDRLINE3    string `xml:"VENDOR_ADDR_LINE3"`
	VENDORCITY         string `xml:"VENDOR_CITY"`
	VENDORSTATE        string `xml:"VENDOR_STATE"`
	VENDORZIP          string `xml:"VENDOR_ZIP"`
	VENDORPHNNUM       string `xml:"VENDOR_PHN_NUM"`
	VENDOREMAILADDR    string `xml:"VENDOR_EMAIL_ADDR"`
	VENDORWEBLINKURL   string `xml:"VENDOR_WEB_LINK_URL"`
	VENDORREASONCODE   string `xml:"VENDOR_REASON_CODE"`
}

// VENDORS ...
type VENDORS struct {
	VENDOR []*VENDOR `xml:"VENDOR"`
}

// POLICY ...
type POLICY struct {
	RECTYPE                       string                `xml:"REC_TYPE"`
	POLTRACKINGID                 string                `xml:"POL_TRACKING_ID"`
	POLEXTERNALTRACKINGID         string                `xml:"POL_EXTERNAL_TRACKING_ID"`
	POLSYSCODE                    string                `xml:"POL_SYS_CODE"`
	POLSOURCE                     string                `xml:"POL_SOURCE"`
	POLSERVICENAME                string                `xml:"POL_SERVICE_NAME"`
	POLSERVICEDESC                string                `xml:"POL_SERVICE_DESC"`
	POLDOCTYPE                    string                `xml:"POL_DOC_TYPE"`
	POLCONT                       string                `xml:"POL_CONT"`
	POLPOLNUM                     string                `xml:"POL_POL_NUM"`
	POLCASEID                     string                `xml:"POL_CASE_ID"`
	POLPRODCODE                   string                `xml:"POL_PROD_CODE"`
	POLLOB                        string                `xml:"POL_LOB"`
	POLQUALTYPE                   string                `xml:"POL_QUAL_TYPE"`
	POLQUALDESC                   string                `xml:"POL_QUAL_DESC"`
	POLPLANCODE                   string                `xml:"POL_PLAN_CODE"`
	POLPLANTYPE                   string                `xml:"POL_PLAN_TYPE"`
	POLPRODUCT                    string                `xml:"POL_PRODUCT"`
	POLTXNDESC                    string                `xml:"POL_TXN_DESC"`
	POLTXNAMT                     float64               `xml:"POL_TXN_AMT"`
	POLPRODSHORTNAME              string                `xml:"POL_PROD_SHORT_NAME"`
	POLPRDCTMKTGNAME              string                `xml:"POL_PRDCT_MKTG_NAME"`
	POLMKTGNAME                   string                `xml:"POL_MKTG_NAME"`
	POLPRODUCTTYPE                string                `xml:"POL_PRODUCT_TYPE"`
	POLCOMPNAME                   string                `xml:"POL_COMP_NAME"`
	POLPRDCTCOMP                  string                `xml:"POL_PRDCT_COMP"`
	POLPRCSGCOMP                  string                `xml:"POL_PRCSG_COMP"`
	POLISSUESTATE                 string                `xml:"POL_ISSUE_STATE"`
	POLRESIDENCESTATE             string                `xml:"POL_RESIDENCE_STATE"`
	POLISSUETYPE                  string                `xml:"POL_ISSUE_TYPE"`
	POLTERM                       int                   `xml:"POL_TERM"`
	POLSTATUS                     string                `xml:"POL_STATUS"`
	POLPREMAMT                    float64               `xml:"POL_PREM_AMT"`
	POLPREMMODE                   string                `xml:"POL_PREM_MODE"`
	POLPAYMENTFREQUENCY           string                `xml:"POL_PAYMENT_FREQUENCY"`
	POLISSUEAGE                   int                   `xml:"POL_ISSUE_AGE"`
	POLCONTAGE                    string                `xml:"POL_CONT_AGE"`
	POLRISKCLASS                  string                `xml:"POL_RISK_CLASS"`
	POLEFFECTIVEDATE              string                `xml:"POL_EFFECTIVE_DATE"`
	POLISSUEDATE                  string                `xml:"POL_ISSUE_DATE"`
	POLSTARTDATE                  string                `xml:"POL_START_DATE"`
	POLMATURITYDATE               string                `xml:"POL_MATURITY_DATE"`
	POLTERMDATE                   string                `xml:"POL_TERM_DATE"`
	POLASOFDATE                   string                `xml:"POL_AS_OF_DATE"`
	POLCURRANNIVBEGINDATE         string                `xml:"POL_CURR_ANNIV_BEGIN_DATE"`
	POLCURRANNIVENDDATE           string                `xml:"POL_CURR_ANNIV_END_DATE"`
	POLLASTANNIVDATE              string                `xml:"POL_LAST_ANNIV_DATE"`
	POLCURRANNIVDATE              string                `xml:"POL_CURR_ANNIV_DATE"`
	POLNEXTANNIVDATE              string                `xml:"POL_NEXT_ANNIV_DATE"`
	POLNEXTANNIVBEGINDATE         string                `xml:"POL_NEXT_ANNIV_BEGIN_DATE"`
	POLNEXTANNIVENDDATE           string                `xml:"POL_NEXT_ANNIV_END_DATE"`
	POLFACEVALUE                  int                   `xml:"POL_FACE_VALUE"`
	POLCYCLEDATE                  string                `xml:"POL_CYCLE_DATE"`
	POLCURRYR                     int                   `xml:"POL_CURR_YR"`
	POLBONUSSTARTDATE             uint8                 `xml:"POL_BONUS_START_DATE"`
	POLGUARANTEEDINTERESTRATEYEAR uint8                 `xml:"POL_GUARANTEED_INTEREST_RATE_YEAR"`
	POLOWNERSAMEASINS             string                `xml:"POL_OWNER_SAME_AS_INS"`
	POLINFORCEASVIND              string                `xml:"POL_INFORCE_ASV_IND"`
	POLCOSTBASIS                  float64               `xml:"POL_COST_BASIS"`
	POLISMEC                      bool                  `xml:"POL_IS_MEC"`
	POLSURRENDERDATE              string                `xml:"POL_SURRENDER_DATE"`
	POLFIXEDCOSTENDPERIOD         uint8                 `xml:"POL_FIXED_COST_END_PERIOD"`
	POLFIXEDCOSTPOSTSTARTPERIOD   string                `xml:"POL_FIXED_COST_POST_START_PERIOD"`
	POLMONTHLYDEDUCTIONDATE       string                `xml:"POL_MONTHLY_DEDUCTION_DATE"`
	POLACCOUNTVALUE               string                `xml:"POL_ACCOUNT_VALUE"`
	POLWITHHOLDINGAMOUNT          string                `xml:"POL_WITHHOLDING_AMOUNT"`
	POLAPIMAGEPATH                string                `xml:"POL_AP_IMAGE_PATH"`
	POLAPIMAGE                    string                `xml:"POL_AP_IMAGE"`
	POLSIGNEDAPP                  string                `xml:"POL_SIGNED_APP"`
	POLSIGNEDOVERFLOWAPP          string                `xml:"POL_SIGNED_OVERFLOW_APP"`
	POLSIGNEDSUPPAPP              string                `xml:"POL_SIGNED_SUPP_APP"`
	POLSIGNEDAMENDMENTFORM        string                `xml:"POL_SIGNED_AMENDMENT_FORM"`
	POLSIGNEDEFTFORM              string                `xml:"POL_SIGNED_EFT_FORM"`
	POLSIGNEDABRDISCLOSURE        string                `xml:"POL_SIGNED_ABR_DISCLOSURE"`
	POLREPLACMENTFORM             string                `xml:"POL_REPLACMENT_FORM"`
	POLHIPAAFORM                  string                `xml:"POL_HIPAA_FORM"`
	POLCONSENTFORM                string                `xml:"POL_CONSENT_FORM"`
	POLSIGNEDILLUSTRATION         string                `xml:"POL_SIGNED_ILLUSTRATION"`
	POLSURRENDERPERIODOPT         string                `xml:"POL_SURRENDER_PERIOD_OPT"`
	POLMIBPOSTNOTICEDISC          string                `xml:"POL_MIB_POST_NOTICE_DISC"`
	POLANNSTARTDATE               string                `xml:"POL_ANN_START_DATE"`
	POLTAXYEAR                    string                `xml:"POL_TAX_YEAR"`
	POLTAXDOCUMENTS               string                `xml:"POL_TAX_DOCUMENTS"`
	POLAPPLICATIONID              string                `xml:"POL_APPLICATION_ID"`
	POLTERMINATIONREASONCODE      string                `xml:"POL_TERMINATION_REASON_CODE"`
	POLAMENDMENTS                 *POLAMENDMENTS        `xml:"POL_AMENDMENTS"`
	POLCOSTBASISAMOUNTBEFORE      string                `xml:"POL_COST_BASIS_AMOUNT_BEFORE"`
	POLCOSTBASISAMOUNTAFTER       string                `xml:"POL_COST_BASIS_AMOUNT_AFTER"`
	DOCUMENTS                     *DOCUMENTS            `xml:"DOCUMENTS"`
	PARTIES                       *PARTIES              `xml:"PARTIES"`
	CARRIER                       *CARRIER              `xml:"CARRIER"`
	CARRIERINFORMATION            *CARRIERINFORMATION   `xml:"CARRIER_INFORMATION"`
	EXCHANGES                     *EXCHANGES            `xml:"EXCHANGES"`
	FINANCIALINFORMATION          *FINANCIALINFORMATION `xml:"FINANCIAL_INFORMATION"`
	POLICYVALUES                  *POLICYVALUES         `xml:"POLICYVALUES"`
	SURRENDERCHARGES              *SURRENDERCHARGES     `xml:"SURRENDERCHARGES"`
	POLICYCOVERAGES               *POLICYCOVERAGES      `xml:"POLICYCOVERAGES"`
	POLICYLOANS                   *POLICYLOANS          `xml:"POLICYLOANS"`
	WITHDRAWALVALUES              *WITHDRAWALVALUES     `xml:"WITHDRAWALVALUES"`
	LOANSEGMENTS                  *LOANSEGMENTS         `xml:"LOANSEGMENTS"`
	POLICYMATCH                   *POLICYMATCH          `xml:"POLICYMATCH"`
	POLICYPREMIUMS                *POLICYPREMIUMS       `xml:"POLICYPREMIUMS"`
	POLICYPAYMENTS                *POLICYPAYMENTS       `xml:"POLICYPAYMENTS"`
	FUNDS                         *FUNDS                `xml:"FUNDS"`
	POLICYWITHDRAWALS             *POLICYWITHDRAWALS    `xml:"POLICYWITHDRAWALS"`
	POLICYBANKINFO                *POLICYBANKINFO       `xml:"POLICYBANKINFO"`
	POLICYFUNDS                   *POLICYFUNDS          `xml:"POLICYFUNDS"`
	FEATURES                      *FEATURES             `xml:"FEATURES"`
	POLICYRIDERS                  *POLICYRIDERS         `xml:"POLICYRIDERS"`
	DEATHBENEFIT                  *DEATHBENEFIT         `xml:"DEATH_BENEFIT"`
	TESTVALUES                    *TESTVALUES           `xml:"TEST_VALUES"`
	ILLUSTRATIONVALUES            *ILLUSTRATIONVALUES   `xml:"ILLUSTRATIONVALUES"`
	POLICYTRANSACTIONS            *POLICYTRANSACTIONS   `xml:"POLICYTRANSACTIONS"`
	TRANSACTIONSUMMARIES          *TRANSACTIONSUMMARIES `xml:"TRANSACTIONSUMMARIES"`
	VENDORS                       *VENDORS              `xml:"VENDORS"`
	POLROLETYPE                 string                `xml:"POL_ROLE_TYPE"` //It is added manually.
	POLFULLNAME				  string                `xml:"POL_FULL_NAME"` //It is added manually.
}
// POLICIES ...
type POLICIES struct {
	POLICY []*POLICY `xml:"POLICY"`
}

// DataServicesLetter ...
type DataServicesLetter struct {
	POLICIES *POLICIES `xml:"POLICIES"`
}

// WITHDRAWALARRTYPE ...
type WITHDRAWALARRTYPE *WITHDRAWALARRTYPE
